<?php

namespace App\Console\Commands;

use App\Models\Permission;
use App\Models\Role;
use App\Models\RolePermission;
use App\Models\User;
use Illuminate\Console\Command;

class CheckCategoryPermissions extends Command
{
    protected $signature = 'check:category-permissions';
    protected $description = 'Check if admin role has category permissions';

    public function handle()
    {
        // Get admin role
        $adminRole = Role::where('name', 'Admin')->first();
        
        if (!$adminRole) {
            $this->error('Admin role not found!');
            return 1;
        }
        
        $this->info("Admin role found with ID: {$adminRole->id}");
        
        // Get category permissions
        $category1 = Permission::where('slug', 'category1')->first();
        $category2 = Permission::where('slug', 'category2')->first();
        
        if (!$category1) {
            $this->error('category1 permission not found!');
            return 1;
        }
        
        if (!$category2) {
            $this->error('category2 permission not found!');
            return 1;
        }
        
        $this->info("category1 permission found with ID: {$category1->id}");
        $this->info("category2 permission found with ID: {$category2->id}");
        
        // Check if admin role has these permissions
        $hasCategory1 = RolePermission::where('role_id', $adminRole->id)
            ->where('permission_id', $category1->id)
            ->exists();
            
        $hasCategory2 = RolePermission::where('role_id', $adminRole->id)
            ->where('permission_id', $category2->id)
            ->exists();
            
        if ($hasCategory1) {
            $this->info('Admin role HAS category1 permission');
        } else {
            $this->error('Admin role DOES NOT HAVE category1 permission');
        }
        
        if ($hasCategory2) {
            $this->info('Admin role HAS category2 permission');
        } else {
            $this->error('Admin role DOES NOT HAVE category2 permission');
        }
        
        // Check the hasPermissions method
        $adminUser = User::whereHas('role', function($q) {
            $q->where('name', 'Admin');
        })->first();
        
        if (!$adminUser) {
            $this->error('Admin user not found!');
            return 1;
        }
        
        $this->info("Admin user found with ID: {$adminUser->id}");
        
        $hasPermCategory1 = $adminUser->hasPermissions('category1', $adminRole->id);
        $hasPermCategory2 = $adminUser->hasPermissions('category2', $adminRole->id);
        
        $this->info("hasPermissions('category1') returns: " . ($hasPermCategory1 ? 'true' : 'false'));
        $this->info("hasPermissions('category2') returns: " . ($hasPermCategory2 ? 'true' : 'false'));
        
        return 0;
    }
}
