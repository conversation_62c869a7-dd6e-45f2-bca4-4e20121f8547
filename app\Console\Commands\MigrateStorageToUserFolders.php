<?php

namespace App\Console\Commands;

use App\Helpers\StorageHelper;
use App\Models\Content;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class MigrateStorageToUserFolders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:migrate-to-user-folders';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate existing files to user-specific folders';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting migration of files to user-specific folders...');
        
        // Get all users
        $users = User::all();
        $this->info('Found ' . $users->count() . ' users');
        
        $bar = $this->output->createProgressBar($users->count());
        $bar->start();
        
        foreach ($users as $user) {
            $this->migrateUserFiles($user);
            $bar->advance();
        }
        
        $bar->finish();
        $this->newLine();
        $this->info('Migration completed successfully!');
        
        return Command::SUCCESS;
    }
    
    /**
     * Migrate files for a specific user
     */
    private function migrateUserFiles(User $user)
    {
        try {
            DB::beginTransaction();
            
            // Ensure user directories exist
            StorageHelper::ensureUserDirectoriesExist($user->id);
            
            // Migrate avatar
            if ($user->avatar) {
                $this->migrateAvatar($user);
            }
            
            // Migrate content files
            $contents = Content::where('author_id', $user->id)->get();
            foreach ($contents as $content) {
                $this->migrateContentFile($content);
            }
            
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("Error migrating files for user {$user->id}: " . $e->getMessage());
            Log::error("Error migrating files for user {$user->id}: " . $e->getMessage());
        }
    }
    
    /**
     * Migrate user avatar
     */
    private function migrateAvatar(User $user)
    {
        if (!$user->avatar || !Storage::disk('public')->exists($user->avatar)) {
            return;
        }
        
        $oldPath = $user->avatar;
        $filename = basename($oldPath);
        $newPath = StorageHelper::getUserAvatarPath($user->id, $filename);
        
        // Copy file to new location
        if ($this->copyFile($oldPath, $newPath)) {
            // Update user record with new path
            $user->avatar = $newPath;
            $user->save();
        }
    }
    
    /**
     * Migrate content file
     */
    private function migrateContentFile(Content $content)
    {
        // Migrate main file
        if ($content->file_path && Storage::disk('public')->exists($content->file_path)) {
            $oldPath = $content->file_path;
            $filename = basename($oldPath);
            $mimeType = StorageHelper::mediaTypeToMimeFolder($content->media_type);
            $newPath = StorageHelper::getUserStoragePath($content->author_id, $mimeType, $filename);
            
            // Copy file to new location
            if ($this->copyFile($oldPath, $newPath)) {
                // Update content record with new path
                $content->file_path = $newPath;
                $content->save();
            }
        }
        
        // Migrate thumbnail if it's not a static thumbnail
        if ($content->thumbnail_path && 
            !in_array($content->thumbnail_path, ['thumbnail/thumbnail_audio.svg', 'thumbnail/thumbnail_pdf.png', 'thumbnail/thumbnail_doc.png']) && 
            Storage::disk('public')->exists($content->thumbnail_path)) {
            
            $oldPath = $content->thumbnail_path;
            $filename = basename($oldPath);
            $newPath = StorageHelper::getUserThumbnailPath($content->author_id, $filename);
            
            // Copy file to new location
            if ($this->copyFile($oldPath, $newPath)) {
                // Update content record with new path
                $content->thumbnail_path = $newPath;
                $content->save();
            }
        }
        
        // Migrate release document
        if ($content->release_document && Storage::disk('public')->exists($content->release_document)) {
            $oldPath = $content->release_document;
            $filename = basename($oldPath);
            $newPath = StorageHelper::getUserReleaseDocumentPath($content->author_id, $filename);
            
            // Copy file to new location
            if ($this->copyFile($oldPath, $newPath)) {
                // Update content record with new path
                $content->release_document = $newPath;
                $content->save();
            }
        }
    }
    
    /**
     * Copy file from old path to new path
     */
    private function copyFile($oldPath, $newPath)
    {
        try {
            // Create directory if it doesn't exist
            $directory = dirname($newPath);
            if (!Storage::disk('public')->exists($directory)) {
                Storage::disk('public')->makeDirectory($directory);
            }
            
            // Copy file
            $fileContents = Storage::disk('public')->get($oldPath);
            Storage::disk('public')->put($newPath, $fileContents);
            
            return true;
        } catch (\Exception $e) {
            Log::error("Error copying file from {$oldPath} to {$newPath}: " . $e->getMessage());
            return false;
        }
    }
}
