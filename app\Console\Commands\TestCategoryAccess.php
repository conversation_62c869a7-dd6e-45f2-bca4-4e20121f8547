<?php

namespace App\Console\Commands;

use App\Models\Role;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;

class TestCategoryAccess extends Command
{
    protected $signature = 'test:category-access';
    protected $description = 'Test if admin can access categories route';

    public function handle()
    {
        // Get admin user
        $adminUser = User::whereHas('role', function($q) {
            $q->where('name', 'Admin');
        })->first();
        
        if (!$adminUser) {
            $this->error('Admin user not found!');
            return 1;
        }
        
        $this->info("Admin user found with ID: {$adminUser->id}");
        
        // Login as admin
        Auth::login($adminUser);
        
        $this->info("Logged in as admin: " . Auth::check());
        
        // Create a request to the categories route
        $request = request();
        $request->setMethod('GET');
        $request->server->set('REQUEST_URI', '/reference/categories');
        
        // Get the middleware
        $middleware = app(\App\Http\Middleware\CheckPermission::class);
        
        // Test the middleware with category1 permission
        $result = $middleware->handle($request, function() {
            return response('OK');
        }, 'category1');
        
        $this->info("Middleware test with 'category1' permission: " . ($result->getContent() === 'OK' ? 'PASSED' : 'FAILED'));
        
        // Test the middleware with category2 permission
        $result = $middleware->handle($request, function() {
            return response('OK');
        }, 'category2');
        
        $this->info("Middleware test with 'category2' permission: " . ($result->getContent() === 'OK' ? 'PASSED' : 'FAILED'));
        
        // Test the middleware with both permissions
        $result = $middleware->handle($request, function() {
            return response('OK');
        }, 'category1', 'category2');
        
        $this->info("Middleware test with both permissions: " . ($result->getContent() === 'OK' ? 'PASSED' : 'FAILED'));
        
        return 0;
    }
}
