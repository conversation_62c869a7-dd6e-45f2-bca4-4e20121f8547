<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;

class StorageHelper
{
    /**
     * Get the storage path for a user's file
     *
     * @param string $userId User ID
     * @param string $mediaType Media type (image, video, audio, application)
     * @param string $filename Filename
     * @return string Storage path
     */
    public static function getUserStoragePath(string $userId, string $mediaType, string $filename): string
    {
        // Convert media type to folder name (lowercase plural)
        $folderName = strtolower($mediaType) . 's';
        
        // Return path in format: users/{user_id}/{media_type}s/{filename}
        return "users/{$userId}/{$folderName}/{$filename}";
    }

    /**
     * Get the thumbnail path for a user's file
     *
     * @param string $userId User ID
     * @param string $filename Filename for the thumbnail
     * @return string Thumbnail path
     */
    public static function getUserThumbnailPath(string $userId, string $filename): string
    {
        // Return path in format: users/{user_id}/thumbnails/{filename}
        return "users/{$userId}/thumbnails/{$filename}";
    }

    /**
     * Get the release document path for a user
     *
     * @param string $userId User ID
     * @param string $filename Filename for the release document
     * @return string Release document path
     */
    public static function getUserReleaseDocumentPath(string $userId, string $filename): string
    {
        // Return path in format: users/{user_id}/release-documents/{filename}
        return "users/{$userId}/release-documents/{$filename}";
    }

    /**
     * Get the avatar path for a user
     *
     * @param string $userId User ID
     * @param string $filename Filename for the avatar
     * @return string Avatar path
     */
    public static function getUserAvatarPath(string $userId, string $filename): string
    {
        // Return path in format: users/{user_id}/avatar/{filename}
        return "users/{$userId}/avatar/{$filename}";
    }

    /**
     * Ensure user directories exist
     *
     * @param string $userId User ID
     * @return void
     */
    public static function ensureUserDirectoriesExist(string $userId): void
    {
        $directories = [
            "users/{$userId}/images",
            "users/{$userId}/videos",
            "users/{$userId}/audios",
            "users/{$userId}/applications",
            "users/{$userId}/thumbnails",
            "users/{$userId}/release-documents",
            "users/{$userId}/avatar",
        ];

        foreach ($directories as $directory) {
            Storage::disk('public')->makeDirectory($directory);
        }
    }

    /**
     * Convert media type to MIME type folder
     *
     * @param string $mediaType Media type (Photo, Illustration, Video, Audio, Document)
     * @return string MIME type folder (image, video, audio, application)
     */
    public static function mediaTypeToMimeFolder(string $mediaType): string
    {
        switch ($mediaType) {
            case 'Photo':
            case 'Illustration':
                return 'image';
            case 'Video':
                return 'video';
            case 'Audio':
                return 'audio';
            case 'Document':
                return 'application';
            default:
                return 'application';
        }
    }

    /**
     * Convert MIME type to media type
     *
     * @param string $mimeType MIME type
     * @return string Media type (Photo, Illustration, Video, Audio, Document)
     */
    public static function mimeTypeToMediaType(string $mimeType): string
    {
        $type = explode('/', $mimeType)[0];
        
        switch ($type) {
            case 'image':
                return 'Photo'; // Default to Photo, can be changed to Illustration later
            case 'video':
                return 'Video';
            case 'audio':
                return 'Audio';
            default:
                return 'Document';
        }
    }
}
