<?php

namespace App\Http\Controllers;

use App\Models\RolePermission;
use App\Services\AuthService;
use Illuminate\Http\Request;

use function PHPSTORM_META\type;

class AuthController extends Controller {
    protected $authService;

    public function __construct(AuthService $authService) {
        $this->authService = $authService;
    }

    public function login(Request $request) {
        $credentials = $request->only(['username', 'password']);

        $user = $this->authService->login($credentials);

        if (!$user) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => config('messages.custom.wrong_creds'),
            ]);
        }

        $permission = RolePermission::where('role_id', $user->role->id)->where('permission_id', 1)->first();

        if($permission) {
            return redirect()->intended('explore')->with([
                'type' => 'welcome',
                'message' => config('messages.custom.welcome')
            ]);
        } else {
            return redirect()->route('explore')->with([
                'type' => 'welcome',
                'message' => config('messages.custom.welcome')
            ]);
        }
        }

    public function logout() {
        $this->authService->logout();
        return redirect('/');
    }
}
