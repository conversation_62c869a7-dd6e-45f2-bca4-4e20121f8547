<?php

namespace App\Http\Controllers;

use App\Services\CategoryService;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    protected $categoryService;

    public function __construct(CategoryService $categoryService)
    {
        $this->categoryService = $categoryService;
    }

    public $page = "Categories";

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $type = request()->query('type', 'all');

        // Filter categories based on user permissions
        $user = auth()->user();
        $role_id = $user->role_id ?? null;

        $hasCategory1Permission = $user->hasPermissions('category1', $role_id);
        $hasCategory2Permission = $user->hasPermissions('category2', $role_id);

        // If user has only one permission, force that type
        // But if user has both permissions, respect their choice or default to 'all'
        if ($hasCategory1Permission && !$hasCategory2Permission) {
            $type = 'general'; // Force Category 1 (Umum) if user only has Category 1 permission
        } elseif (!$hasCategory1Permission && $hasCategory2Permission) {
            $type = 'subject'; // Force Category 2 (<PERSON>) if user only has Category 2 permission
        }
        // If user has both permissions, we don't need to force anything

        // Check specific permissions
        $canEditCategory1 = $user->hasPermissions('edit.category1', $role_id);
        $canEditCategory2 = $user->hasPermissions('edit.category2', $role_id);
        $canDeleteCategory1 = $user->hasPermissions('delete.category1', $role_id);
        $canDeleteCategory2 = $user->hasPermissions('delete.category2', $role_id);

        // Log permissions for debugging
        \Illuminate\Support\Facades\Log::info("User permissions for categories:", [
            'canEditCategory1' => $canEditCategory1,
            'canEditCategory2' => $canEditCategory2,
            'canDeleteCategory1' => $canDeleteCategory1,
            'canDeleteCategory2' => $canDeleteCategory2
        ]);

        $data = [
            'title' => 'List',
            'page' => 'Categories',
            'type' => $type,
            'userPermissions' => [
                'canEditCategory1' => $canEditCategory1,
                'canEditCategory2' => $canEditCategory2,
                'canDeleteCategory1' => $canDeleteCategory1,
                'canDeleteCategory2' => $canDeleteCategory2
            ]
        ];

        return view('pages.references.categories.index', $data);
    }

    public function categories_datatable(Request $request)
    {
        $type = $request->query('type', 'all');

        // Filter categories based on user permissions
        $user = auth()->user();
        $role_id = $user->role_id ?? null;

        $hasCategory1Permission = $user->hasPermissions('category1', $role_id);
        $hasCategory2Permission = $user->hasPermissions('category2', $role_id);

        // If user has only one permission, filter by that type
        // But if user has both permissions, respect their choice
        if ($hasCategory1Permission && !$hasCategory2Permission) {
            $type = 'general'; // Force Category 1 (Umum) if user only has Category 1 permission
        } elseif (!$hasCategory1Permission && $hasCategory2Permission) {
            $type = 'subject'; // Force Category 2 (Mata Pelajaran) if user only has Category 2 permission
        }
        // If user has both permissions, we don't need to force anything

        $data = $this->categoryService->getAll($type);
        return $data;
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $type = $request->query('type', 'general');

        $data = [
            'title' => 'Create',
            'page' => $this->page,
            'type' => $type
        ];

        return view('pages.references.categories.form', $data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $this->categoryService->create($request->except('_token'));

            return redirect()->route('categories')->with([
                'type' => 'success',
                'message' => config('messages.success.create'),
            ]);
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $e->getCode() == 400 ? $e->getMessage() : config('messages.error.create'),
            ], $e->getCode() ?: 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(int $id)
    {
        $data = [
            'title' => 'Edit',
            'page' => $this->page,
            'data' => $this->categoryService->findOne($id)
        ];

        return view('pages.references.categories.form', $data);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, int $id)
    {
        try {
            $this->categoryService->update($id, $request->all());

            return redirect()->route('categories')->with([
                'type' => 'success',
                'message' => config('messages.success.update'),
            ]);
        } catch (\Exception $e) {
            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $e->getCode() == 400 ? $e->getMessage() : config('messages.error.update'),
            ], $e->getCode() ?: 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id)
    {
        try {
            $this->categoryService->delete($id);

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.delete'),
                'data' => null
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => config('messages.error.delete')
            ], 500);
        }
    }

    /**
     * Get categories by type
     *
     * @param string $type The type of categories to retrieve ('general', 'subject', or 'all')
     * @return \Illuminate\Http\Response|\Illuminate\Http\JsonResponse
     */
    public function getCategoriesByType(string $type)
    {
        try {
            // Check if the request wants JSON (API call) or HTML (view)
            $wantsJson = request()->expectsJson() || request()->ajax();

            // Filter categories based on user permissions
            $user = auth()->user();
            $role_id = $user->role_id ?? null;

            $hasCategory1Permission = $user->hasPermissions('category1', $role_id);
            $hasCategory2Permission = $user->hasPermissions('category2', $role_id);

            // If user has only one permission, filter by that type
            // But if user has both permissions, respect their choice
            $originalType = $type;
            if ($hasCategory1Permission && !$hasCategory2Permission) {
                $type = 'general'; // Category 1 (Umum) if user only has Category 1 permission
            } elseif (!$hasCategory1Permission && $hasCategory2Permission) {
                $type = 'subject'; // Category 2 (Mata Pelajaran) if user only has Category 2 permission
            } elseif (!$hasCategory1Permission && !$hasCategory2Permission) {
                // User has no permission to view any categories
                $categories = [];

                if ($wantsJson) {
                    return response()->json([
                        'status' => 'success',
                        'message' => 'Categories retrieved successfully',
                        'data' => []
                    ], 200);
                } else {
                    $typeLabel = $originalType === 'general' ? 'Category 1 (Umum)' : ($originalType === 'subject' ? 'Category 2 (Mata Pelajaran)' : 'All Categories');

                    $data = [
                        'title' => 'List',
                        'page' => 'Categories - ' . $typeLabel,
                        'type' => $originalType,
                        'categories' => []
                    ];

                    return view('pages.references.categories.category_view', $data);
                }
            }

            $categories = $this->categoryService->getCategoriesByType($type);

            if ($wantsJson) {
                // Return JSON response for API calls
                // Check specific permissions for JSON response
                $canEditCategory1 = $user->hasPermissions('edit.category1', $role_id);
                $canEditCategory2 = $user->hasPermissions('edit.category2', $role_id);
                $canDeleteCategory1 = $user->hasPermissions('delete.category1', $role_id);
                $canDeleteCategory2 = $user->hasPermissions('delete.category2', $role_id);

                // Log permissions for debugging
                \Illuminate\Support\Facades\Log::info("User permissions for categories (JSON response):", [
                    'canEditCategory1' => $canEditCategory1,
                    'canEditCategory2' => $canEditCategory2,
                    'canDeleteCategory1' => $canDeleteCategory1,
                    'canDeleteCategory2' => $canDeleteCategory2
                ]);

                return response()->json([
                    'status' => 'success',
                    'message' => 'Categories retrieved successfully',
                    'data' => $categories,
                    'permissions' => [
                        'canEditCategory1' => $canEditCategory1,
                        'canEditCategory2' => $canEditCategory2,
                        'canDeleteCategory1' => $canDeleteCategory1,
                        'canDeleteCategory2' => $canDeleteCategory2
                    ]
                ], 200);
            } else {
                // Return view for direct browser access
                $typeLabel = $originalType === 'general' ? 'Category 1 (Umum)' : ($originalType === 'subject' ? 'Category 2 (Mata Pelajaran)' : 'All Categories');

                // Check specific permissions
                $canEditCategory1 = $user->hasPermissions('edit.category1', $role_id);
                $canEditCategory2 = $user->hasPermissions('edit.category2', $role_id);
                $canDeleteCategory1 = $user->hasPermissions('delete.category1', $role_id);
                $canDeleteCategory2 = $user->hasPermissions('delete.category2', $role_id);

                // Log permissions for debugging
                \Illuminate\Support\Facades\Log::info("User permissions for categories (getCategoriesByType):", [
                    'canEditCategory1' => $canEditCategory1,
                    'canEditCategory2' => $canEditCategory2,
                    'canDeleteCategory1' => $canDeleteCategory1,
                    'canDeleteCategory2' => $canDeleteCategory2
                ]);

                $data = [
                    'title' => 'List',
                    'page' => 'Categories - ' . $typeLabel,
                    'type' => $originalType,
                    'categories' => $categories,
                    'userPermissions' => [
                        'canEditCategory1' => $canEditCategory1,
                        'canEditCategory2' => $canEditCategory2,
                        'canDeleteCategory1' => $canDeleteCategory1,
                        'canDeleteCategory2' => $canDeleteCategory2
                    ]
                ];

                return view('pages.references.categories.category_view', $data);
            }
        } catch (\Exception $e) {
            // Log the error for debugging
            \Illuminate\Support\Facades\Log::error('Category retrieval error: ' . $e->getMessage());

            if (request()->expectsJson() || request()->ajax()) {
                // For AJAX requests, return an empty array instead of an error
                // This prevents the error notification while still allowing the page to function
                return response()->json([
                    'status' => 'success',
                    'message' => 'Categories retrieved successfully',
                    'data' => []
                ], 200);
            } else {
                // Instead of showing an error, just redirect to the categories page
                // This prevents the error notification
                return redirect()->route('categories');
            }
        }
    }
}
