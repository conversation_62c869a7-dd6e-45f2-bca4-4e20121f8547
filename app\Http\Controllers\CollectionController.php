<?php

namespace App\Http\Controllers;

use App\Services\CollectionService;
use Illuminate\Http\Request;

class CollectionController extends Controller
{
    protected $collectionService;

    public function __construct(CollectionService $collectionService)
    {
        $this->collectionService = $collectionService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        try {
            $user_id = auth()->user()->id;
            $data = $this->collectionService->getAllCollections($user_id);

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.get'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function get_paginate()
    {
        try {
            $user_id = auth()->user()->id;
            $data = $this->collectionService->getCollections($user_id);

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.get'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $request['user_id'] = auth()->user()->id;
            $data = $this->collectionService->create($request->all());

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.create'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function add_content(Request $request)
    {
        try {
            $data = $this->collectionService->add($request->all());

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.add_item_collection'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ],$e->getCode() ?: 500);
        }
    }

    public function remove_content(Request $request)
    {
        try {
            $data = $this->collectionService->remove($request->all());

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.remove_item_collection'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ],$e->getCode() ?: 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id)
    {
        try {
            $data = $this->collectionService->getCollectionDetail($id);
            
            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.get'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function show_content(string $id)
    {
        try {
            $data = $this->collectionService->getContentDetail($id);
            
            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.get'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, int $id)
    {
        try {
            $this->collectionService->update($id, $request->name);

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.update'),
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ],$e->getCode() ?: 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id)
    {
        try {
            $this->collectionService->delete($id);

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.delete'),
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ],$e->getCode() ?: 500);
        }
    }
}
