<?php

namespace App\Http\Controllers;

use App\Http\Requests\ContentRequest;
use App\Models\Content;
use App\Models\Keyword;
use App\Services\ContentService;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class ContentController extends Controller
{
    protected $contentService;

    public function __construct(ContentService $contentService)
    {
        $this->contentService = $contentService;
    }

    public $page = "Submit Content";

    public function index()
    {
        $data = [
            'page' => $this->page,
            'categories' => $this->contentService->getCategory('all'),
            'general_categories' => $this->contentService->getCategory('all', 'general'),
            'subject_categories' => $this->contentService->getCategory('all', 'subject')
        ];
        return view('pages.submit-content.index', $data);
    }

    public function get_contents(Request $request)
    {
        try {
            // Get the content first
            $content = $this->contentService->getContentByAuthor($request->all());

            // Get counts directly from the database for all statuses
            $countArray = [];
            $authorId = $request->author;

            // Get count for Draft
            $countArray['draft'] = Content::where('status', 'Draft')
                ->where('author_id', $authorId)->count();
            $countArray['draft_image'] = Content::where('status', 'Draft')
                ->where('author_id', $authorId)
                ->whereIn('media_type', ['Photo', 'Illustration'])->count();
            $countArray['draft_video'] = Content::where('status', 'Draft')
                ->where('author_id', $authorId)
                ->where('media_type', 'Video')->count();
            $countArray['draft_audio'] = Content::where('status', 'Draft')
                ->where('author_id', $authorId)
                ->where('media_type', 'Audio')->count();
            $countArray['draft_document'] = Content::where('status', 'Draft')
                ->where('author_id', $authorId)
                ->where('media_type', 'Document')->count();

            // Get count for Pending
            $countArray['pending'] = Content::where('status', 'Pending')
                ->where('author_id', $authorId)->count();
            $countArray['pending_image'] = Content::where('status', 'Pending')
                ->where('author_id', $authorId)
                ->whereIn('media_type', ['Photo', 'Illustration'])->count();
            $countArray['pending_video'] = Content::where('status', 'Pending')
                ->where('author_id', $authorId)
                ->where('media_type', 'Video')->count();
            $countArray['pending_audio'] = Content::where('status', 'Pending')
                ->where('author_id', $authorId)
                ->where('media_type', 'Audio')->count();
            $countArray['pending_document'] = Content::where('status', 'Pending')
                ->where('author_id', $authorId)
                ->where('media_type', 'Document')->count();

            // Get count for Rejected (last 14 days)
            $fourteenDaysAgo = now()->subDays(14);
            $countArray['rejected'] = Content::where('status', 'Rejected')
                ->where('author_id', $authorId)
                ->where('updated_at', '>=', $fourteenDaysAgo)->count();
            $countArray['rejected_image'] = Content::where('status', 'Rejected')
                ->where('author_id', $authorId)
                ->where('updated_at', '>=', $fourteenDaysAgo)
                ->whereIn('media_type', ['Photo', 'Illustration'])->count();
            $countArray['rejected_video'] = Content::where('status', 'Rejected')
                ->where('author_id', $authorId)
                ->where('updated_at', '>=', $fourteenDaysAgo)
                ->where('media_type', 'Video')->count();
            $countArray['rejected_audio'] = Content::where('status', 'Rejected')
                ->where('author_id', $authorId)
                ->where('updated_at', '>=', $fourteenDaysAgo)
                ->where('media_type', 'Audio')->count();
            $countArray['rejected_document'] = Content::where('status', 'Rejected')
                ->where('author_id', $authorId)
                ->where('updated_at', '>=', $fourteenDaysAgo)
                ->where('media_type', 'Document')->count();

            // Get count for Approved
            $countArray['approved'] = Content::where('status', 'Approved')
                ->where('author_id', $authorId)->count();
            $countArray['approved_image'] = Content::where('status', 'Approved')
                ->where('author_id', $authorId)
                ->whereIn('media_type', ['Photo', 'Illustration'])->count();
            $countArray['approved_video'] = Content::where('status', 'Approved')
                ->where('author_id', $authorId)
                ->where('media_type', 'Video')->count();
            $countArray['approved_audio'] = Content::where('status', 'Approved')
                ->where('author_id', $authorId)
                ->where('media_type', 'Audio')->count();
            $countArray['approved_document'] = Content::where('status', 'Approved')
                ->where('author_id', $authorId)
                ->where('media_type', 'Document')->count();

            // Get count for Published
            $countArray['published'] = Content::where('status', 'Published')
                ->where('author_id', $authorId)->count();
            $countArray['published_image'] = Content::where('status', 'Published')
                ->where('author_id', $authorId)
                ->whereIn('media_type', ['Photo', 'Illustration'])->count();
            $countArray['published_video'] = Content::where('status', 'Published')
                ->where('author_id', $authorId)
                ->where('media_type', 'Video')->count();
            $countArray['published_audio'] = Content::where('status', 'Published')
                ->where('author_id', $authorId)
                ->where('media_type', 'Audio')->count();
            $countArray['published_document'] = Content::where('status', 'Published')
                ->where('author_id', $authorId)
                ->where('media_type', 'Document')->count();

            // Calculate Reviewed (Approved + Rejected)
            $countArray['reviewed'] = $countArray['approved'] + $countArray['rejected'];
            $countArray['reviewed_image'] = $countArray['approved_image'] + $countArray['rejected_image'];
            $countArray['reviewed_video'] = $countArray['approved_video'] + $countArray['rejected_video'];
            $countArray['reviewed_audio'] = $countArray['approved_audio'] + $countArray['rejected_audio'];
            $countArray['reviewed_document'] = $countArray['approved_document'] + $countArray['rejected_document'];

            $data = [
                'count' => $countArray,
                'content' => $content
            ];

            return response()->json([
                'success' => true,
                'message' => config('messages.success.get'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => config('messages.error.get'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function get_reviews($id)
    {
        try {
            $data = $this->contentService->getReviews($id);

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.get'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => config('messages.error.get')
            ], 500);
        }
    }

    public function search_keywords(Request $request)
    {
        $data = $this->contentService->searchKeywords($request->input('query'));

        if(!$data){
            return response()->json([
                'success' => false,
                'message' => config('messages.error.get')
            ], 500);
        }

        return response()->json([
            'success' => true,
            'message' => config('messages.success.get'),
            'data' => $data
        ], 200);
    }

    public function get_all_keywords()
    {
        $data = $this->contentService->getAllKeywords('all');

        if(!$data){
            return response()->json([
                'success' => false,
                'message' => config('messages.error.get')
            ], 500);
        }

        return response()->json([
            'success' => true,
            'message' => config('messages.success.get'),
            'data' => $data
        ], 200);
    }

    public function get_keywords(string $id)
    {
        $data = $this->contentService->getKeywords($id);

        if(!$data){
            return response()->json([
                'success' => false,
                'message' => config('messages.error.get')
            ], 500);
        }

        return response()->json([
            'success' => true,
            'message' => config('messages.success.get'),
            'data' => $data
        ], 200);
    }

    public function upload(Request $request)
    {
        $created = $this->contentService->create($request->all());

        if(!$created){
            session()->flash('type', 'error');
            session()->flash('message', config('messages.error.create'));

            return response()->json([
                'status' => session('type'),
                'message' => session('message'),
            ], 500);
        }

        session()->flash('type', 'success');
        session()->flash('message', config('messages.success.create'));

        return response()->json([
            'status' => session('type'),
            'message' => session('message')
        ]);
    }

    public function update(ContentRequest $request)
    {
        try {
            $validatedData = $request->validated();

            if ($request->hasFile('release_document')) {
                $validatedData['release_document'] = $request->file('release_document');
            }

            $updated = $this->contentService->update($validatedData);

            if (!$updated) {
                return redirect()->back()->with([
                    'type' => 'error',
                    'message' => config('messages.error.' . $request->update_type),
                ]);
            }

            return redirect()->back()->with([
                'type' => 'success',
                'message' => config('messages.success.' . $request->update_type),
            ]);
        } catch (ValidationException $e) {
            return redirect()->back()
                ->withErrors($e->validator)
                ->withInput();
        } catch (\Exception $e) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => $e->getCode() == 400 ? $e->getMessage() : config('messages.error.' . $request->update_type),
            ]);
        }
    }

    public function destroy(string $id)
    {
        try {
            $this->contentService->delete($id);

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.delete'),
                'data' => null
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => config('messages.error.delete')
            ], $e->getCode() ?: 500);
        }
    }

    public function bulkDestroy(Request $request)
    {
        try {
            $this->contentService->bulkDelete($request->ids);

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.bulk_delete'),
                'data' => null
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => config('messages.error.bulk_delete')
            ], $e->getCode() ?: 500);
        }
    }
}
