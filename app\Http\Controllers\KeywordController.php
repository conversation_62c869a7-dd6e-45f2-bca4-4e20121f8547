<?php

namespace App\Http\Controllers;

use App\Services\KeywordService;
use Illuminate\Http\Request;

class KeywordController extends Controller
{
    protected $keywordService;

    public function __construct(KeywordService $keywordService)
    {
        $this->keywordService = $keywordService;
    }

    public $page = "Keywords";
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data = [
            'title' => 'List',
            'page' => $this->page
        ];
        
        return view('pages.references.keywords.index', $data);
    }

    public function keywords_datatable()
    {
        $data = $this->keywordService->getAll();
        return $data;
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data = [
            'title' => 'Create',
            'page' => $this->page
        ];
        
        return view('pages.references.keywords.form', $data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $this->keywordService->create($request->except('_token'));

            return redirect()->route('keywords')->with([
                'type' => 'success',
                'message' => config('messages.success.create'),
            ], 200);
        } catch (\Exception $e) {

            return redirect()->back()->withInput()->with([
                'type' => 'error',
                'message' => $e->getCode() == 400 ? $e->getMessage() : config('messages.success.create'),
            ], $e->getCode() ?: 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(int $id)
    {
        $data = [
            'title' => 'Edit',
            'page' => $this->page,
            'data' => $this->keywordService->findOne($id)
        ];
        
        return view('pages.references.keywords.form', $data);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, int $id)
    {
        $updated = $this->keywordService->update($id, $request->all());

        if(!$updated){
            return redirect()->back()->with([
                'type' => 'error',
                'message' => config('messages.error.update'),
            ]);
        }

        return redirect()->back()->with([
            'type' => 'success',
            'message' => config('messages.success.update'),
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id)
    {
        try {
            $this->keywordService->delete($id);

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.delete'),
                'data' => null
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => config('messages.error.delete')
            ], 500);
        }
    }
}
