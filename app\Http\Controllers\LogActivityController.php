<?php

namespace App\Http\Controllers;

use App\Services\LogActivityService;
use Illuminate\Http\Request;

class LogActivityController extends Controller
{
    protected $logActivityService;

    public function __construct(LogActivityService $logActivityService) {
        $this->logActivityService = $logActivityService;
    }

    public function index()
    {
        return view('pages.manage.activity.index');
    }

    public function activities_year(Request $request)
    {
        try {
            $user_id = $request->query('userId') ?? null;
            $data = $this->logActivityService->getActivityYears($user_id);
            
            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.get'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function activities(Request $request)
    {
        try {
            $user_id = $request->query('userId') ?? null;
            $data = $this->logActivityService->getActivities($request->query('year'), $user_id);
            
            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.get'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
