<?php

namespace App\Http\Controllers;

use App\Services\NotificationService;
use Illuminate\Http\Request;

class NotificationController extends Controller
{
    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    public function index()
    {
        try {
            $data = $this->notificationService->getNotifs(auth()->user()->id);
            
            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.get'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function read(Request $request)
    {
        try {
            $data = $this->notificationService->readNotif($request->id);
            
            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.update'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function read_all()
    {
        try {
            $data = $this->notificationService->readAllNotif();
            
            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.update'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
