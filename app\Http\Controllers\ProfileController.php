<?php

namespace App\Http\Controllers;

use App\Http\Requests\UserRequest;
use App\Services\LogActivityService;
use App\Services\ProfileService;
use Illuminate\Http\Request;

class ProfileController extends Controller
{
    protected $profileService, $logActivityService;

    public function __construct(ProfileService $profileService, LogActivityService $logActivityService) {
        $this->profileService = $profileService;
        $this->logActivityService = $logActivityService;
    }

    public function index()
    {
        $data['profile'] = $this->profileService->getProfile();
        $data['activity'] = $this->logActivityService->getRecentActivity(auth()->user()->id);
        return view('pages.profiles.index', $data);
    }

    public function update(UserRequest $request)
    {
        $validatedData = $request->validated();

        $updated = $this->profileService->update($request->id, $validatedData);

        if(!$updated){
            return redirect()->back()->with([
                'type' => 'error',
                'message' => config('messages.error.update'),
            ]);
        }

        return redirect()->back()->with([
            'type' => 'success',
            'message' => config('messages.success.update'),
        ]);

    }

    public function works()
    {
        try {
            $data = $this->profileService->getWorks();

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.get'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function favorites()
    {
        try {
            $data = $this->profileService->getFavorites();

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.get'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function show_content(string $id)
    {
        try {
            $data = $this->profileService->showContent($id);

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.get'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function remove_favorite(string $id)
    {
        try {
            $data = $this->profileService->removeFavorite($id);

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.dislike'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Convert a DOC/DOCX file to PDF for preview
     *
     * @param int $id Content ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function convertDocumentForPreview($id)
    {
        try {
            // Get the content item
            $content = $this->profileService->showContent($id);

            if (!$content) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Content not found'
                ], 404);
            }

            // Check if it's a document
            if ($content->media_type !== 'Document') {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Content is not a document'
                ], 400);
            }

            // Check file extension
            $extension = pathinfo($content->file_path, PATHINFO_EXTENSION);
            if (!in_array(strtolower($extension), ['doc', 'docx'])) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'File is not a DOC/DOCX document'
                ], 400);
            }

            // Inject DocumentConversionService
            $documentConversionService = app(\App\Services\DocumentConversionService::class);

            // Check if PDF version already exists
            $pdfPath = $documentConversionService->getPdfVersion($content->file_path);

            // If not, convert it
            if (!$pdfPath) {
                $pdfPath = $documentConversionService->convertToPdf($content->file_path);

                if (!$pdfPath) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Failed to convert document to PDF'
                    ], 500);
                }
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Document converted successfully',
                'data' => [
                    'pdf_path' => $pdfPath
                ]
            ], 200);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Document conversion error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Error converting document: ' . $e->getMessage()
            ], 500);
        }
    }
}
