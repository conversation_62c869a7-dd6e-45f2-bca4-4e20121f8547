<?php

namespace App\Http\Controllers;

use App\Services\RoleService;
use Illuminate\Http\Request;

class Role<PERSON>ontroller extends Controller
{
    protected $roleService;

    public function __construct(RoleService $userService) {
        $this->roleService = $userService;
    }

    public $page = 'Roles';

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data = [
            'title' => 'List',
            'page' => $this->page
        ];
        
        return view('pages.manage.roles.index', $data);
    }

    public function roles_dataTable()
    {
        $data = $this->roleService->getAll();
        return $data;
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data = [
            'title' => 'Create',
            'page' => $this->page,
            'permissions' => $this->roleService->getPermission()
        ];

        return view('pages.manage.roles.form', $data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $created = $this->roleService->create($request->all());

        if(!$created){
            return redirect()->back()->with([
                'type' => 'error',
                'message' => config('messages.error.create'),
            ]);
        }

        return redirect()->route('roles')->with([
            'type' => 'success',
            'message' => config('messages.success.create'),
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(int $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(int $id)
    {
        $data = [
            'title' => 'Edit',
            'page' => $this->page,
            'data' => $this->roleService->findOne($id),
            'permissions' =>$this->roleService->getPermission()
        ];

        return view('pages.manage.roles.form', $data);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, int $id)
    {
        $updated = $this->roleService->update($id, $request->all());
        
        if(!$updated){
            return redirect()->back()->with([
                'type' => 'error',
                'message' => config('messages.error.update'),
            ]);
        }

        return redirect()->back()->with([
            'type' => 'success',
            'message' => config('messages.success.update'),
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(int $id)
    {
        $deleted = $this->roleService->delete($id);

        if($deleted){
            session()->flash('type', 'success');
            session()->flash('message', config('messages.success.delete'));

            return response()->json([
                'status' => session('type'),
                'message' => session('message'),
                'data' => null
            ]);
        }

        session()->flash('type', 'error');
        session()->flash('message', config('messages.error.delete'));

        return response()->json([
            'status' => session('type'),
            'message' => session('message'),
        ], 500);
    }
}
