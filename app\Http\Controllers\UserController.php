<?php

namespace App\Http\Controllers;

use App\Http\Requests\UserRequest;
use App\Services\UserService;
use Illuminate\Http\Request;
class UserController extends Controller
{
    protected $userService;

    public function __construct(UserService $userService) {
        $this->userService = $userService;
    }

    public $page = 'Account';
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $data = [
            'title' => 'List',
            'page' => $this->page
        ];

        return view('pages.manage.account.index', $data);
    }

    public function users_dataTable()
    {
        $data = $this->userService->getAll();

        return $data;
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $data = [
            'title' => 'Create',
            'page' => $this->page,
            'roles' => $this->userService->getRoles()
        ];

        return view('pages.manage.account.form', $data);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(UserRequest $request)
    {
        $validatedData = $request->validated();

        if($request->hasFile('avatar')) $validatedData['avatar'] = $request->file('avatar');

        $created = $this->userService->create($validatedData);

        if(!$created){
            return redirect()->back()->with([
                'type' => 'error',
                'message' => config('messages.error.create'),
            ]);
        }

        return redirect()->route('account')->with([
            'type' => 'success',
            'message' => config('messages.success.create'),
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        $data = [
            'data' => $this->userService->findOne($id),
            'title' => 'Edit',
            'page' => $this->page,
            'roles' => $this->userService->getRoles()
        ];

        return view('pages.manage.account.form', $data);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UserRequest $request, string $id)
    {
        $validatedData = $request->validated();

        if($request->hasFile('avatar')) $validatedData['avatar'] = $request->file('avatar');
        $updated = $this->userService->update($id, $validatedData);

        if(!$updated){
            return redirect()->back()->with([
                'type' => 'error',
                'message' => config('messages.error.update'),
            ]);
        }

        return redirect()->back()->with([
            'type' => 'success',
            'message' => config('messages.success.update'),
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $deleted = $this->userService->delete($id);

        if($deleted){
            session()->flash('type', 'success');
            session()->flash('message', config('messages.success.delete'));

            return response()->json([
                'status' => session('type'),
                'message' => session('message'),
                'data' => null
            ]);
        }

        session()->flash('type', 'error');
        session()->flash('message', config('messages.error.delete'));

        return response()->json([
            'status' => session('type'),
            'message' => session('message'),
        ], 500);
    }
}
