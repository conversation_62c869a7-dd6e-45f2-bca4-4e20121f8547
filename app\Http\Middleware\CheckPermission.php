<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle($request, Closure $next, ...$permissions)
    {
        $user = auth()->user();
        $role_id = $user->role_id ?? null;

        // Jika user tidak login, langsung tolak akses
        if (!$user) {
            return redirect()->back()->with([
                'type' => 'error',
                'message' => 'Silahkan login terlebih dahulu!',
            ], 403);
        }

        // Cek apakah user memiliki salah satu dari permission yang diberikan
        foreach ($permissions as $permission) {
            // Check if the permission contains a pipe (OR condition)
            if (strpos($permission, '|') !== false) {
                $orPermissions = explode('|', $permission);
                foreach ($orPermissions as $orPermission) {
                    if ($user && $user->hasPermissions($orPermission, $role_id)) {
                        return $next($request);
                    }
                }
            } else {
                if ($user && $user->hasPermissions($permission, $role_id)) {
                    return $next($request);
                }
            }
        }

        // Jika tidak ada permission yang sesuai, tolak akses
        return redirect()->back()->with([
            'type' => 'error',
            'message' => 'Anda tidak memiliki akses!',
        ], 403);
    }
}
