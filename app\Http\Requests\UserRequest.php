<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class UserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'name' => 'required|string|max:255',
            'username' => 'required|string|max:255',
            'email' => 'required|email',
            'password' => $this->isMethod('post') ? 'required|string|min:8' : 'nullable|string|min:8',
            'since' => 'nullable|date',
            'avatar' => 'image|max:2048',
            'role_id' => 'required',
        ];

        // Add unique email validation with proper UUID handling
        if ($this->isMethod('post')) {
            // For create - simple unique check
            $rules['email'] .= '|unique:users,email';
        } else {
            // For update - exclude current user from unique check
            $id = $this->route('id');
            if ($id) {
                $rules['email'] .= '|unique:users,email,' . $id;
            }
        }

        return $rules;
    }

    public function messages()
    {
        return [
            'name.required' => 'Name is required!',
            'username.required' => 'Username is required!',
            'email.required' => 'Email is required!',
            'email.email' => 'Please provide a valid email address!',
            'password.required' => 'Password is required!',
            'password.min' => 'Password must be at least 8 characters long!',
            'avatar.max' => 'Maximum file size allowed is 2MB.'
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        return redirect()->back()->with([
            'type' => 'error',
            'message' => config('messages.error.create'),
            'errors' => $validator->errors(),
        ]);
        // throw new HttpResponseException(response()->json([
        //     'success' => false,
        //     'message' => 'Validation failed',
        //     'errors' => $validator->errors(),
        // ], 422));
    }
}