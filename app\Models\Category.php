<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'type',
    ];

    /**
     * Get the type label for display
     *
     * @return string
     */
    public function getTypeLabelAttribute()
    {
        return $this->type === 'general' ? 'Category 1 (Umum)' : 'Category 2 (Mata Pelajaran)';
    }

    /**
     * Scope a query to only include categories of a given type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string  $type
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfType($query, $type)
    {
        if ($type === 'all') {
            return $query;
        }
        
        return $query->where('type', $type);
    }
}
