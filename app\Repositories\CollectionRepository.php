<?php

namespace App\Repositories;

use App\Models\Collection;
use App\Repositories\Contracts\CollectionRepositoryInterface;

class CollectionRepository implements CollectionRepositoryInterface
{
  public function findOne(int $id)
  {
    return Collection::findOrFail($id);
  }

  public function getAllUserCollections(string $userId)
  {
    return Collection::with('contents')->where('user_id', $userId)->get();
  }

  public function getCollectionDetail(int $id)
  {
    $collection = Collection::find($id);
    $contents = $collection->contents()->paginate(8);

    $data = [
      'collection' => $collection,
      'contents' => $contents
    ];

    return $data;
  }

  public function getUserCollections(string $userId)
  {
    return Collection::with('contents')->where('user_id', $userId)->paginate(8);
  }

  public function create(array $data)
  {
    return Collection::create($data);
  }

  public function update(int $id, array $data)
  {
    $collection = $this->findOne($id);

    if(!$collection) return $collection;

    return $collection->update($data);
  }

  public function delete(int $id)
  {
    $collection = $this->findOne($id);

    if(!$collection) return $collection;

    return $collection->delete();
  }
}