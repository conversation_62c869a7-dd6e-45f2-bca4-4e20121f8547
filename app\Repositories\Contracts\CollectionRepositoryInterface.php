<?php

namespace App\Repositories\Contracts;

interface CollectionRepositoryInterface
{
  public function findOne(int $id);
  public function getAllUserCollections(string $userId);
  public function getCollectionDetail(int $id);
  public function getUserCollections(string $userId);
  public function create(array $data);
  public function update(int $id, array $data);
  public function delete(int $id);
}