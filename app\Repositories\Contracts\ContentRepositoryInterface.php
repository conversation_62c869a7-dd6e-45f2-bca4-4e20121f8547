<?php

namespace App\Repositories\Contracts;

interface ContentRepositoryInterface
{
  public function findOne(string $id);
  public function getContent(array $query);
  public function getPublishedContent(array $query);
  public function countContent();
  public function countContentByAuthor(string $author);
  public function create(array $data);
  public function update(string $id, array $data);
  public function delete(string $id);
  public function bulkDelete(array $id);
}