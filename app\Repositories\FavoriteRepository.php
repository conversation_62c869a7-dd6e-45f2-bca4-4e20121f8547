<?php

namespace App\Repositories;

use App\Models\Favorite;
use App\Repositories\Contracts\FavoriteRepositoryInterface;

class FavoriteRepository implements FavoriteRepositoryInterface
{
  public function find(array $data)
  {
    return Favorite::where('user_id', $data['user_id'])->where('content_id', $data['content_id'])->first();
  }

  public function getUserFavorites(string $userId)
  {
    return Favorite::with(['content'])->where('user_id', $userId)->paginate(4);
  }

  public function add(array $data)
  {
    return Favorite::create($data);
  }

  public function remove(int $id)
  {
    $favorite = Favorite::find($id);

    if(!$favorite) return $favorite;

    return $favorite->delete();
  }
}