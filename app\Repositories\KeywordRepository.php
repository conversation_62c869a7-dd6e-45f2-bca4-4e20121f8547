<?php

namespace App\Repositories;

use App\Models\Keyword;
use App\Repositories\Contracts\KeywordRepositoryInterface;

class KeywordRepository implements KeywordRepositoryInterface
{
  public function getKeyword(string $type)
  {
    if($type == 'all'){
      return Keyword::all();
    }else{
      return Keyword::paginate(10);
    }
  }

  public function search(string $query)
  {
    return Keyword::where('name', 'like', "%$query%")->pluck('name');
  }

  public function findOne(int $id)
  {
    return Keyword::find($id);
  }

  public function findExisting(string $name)
  {
    return Keyword::where('name', $name)->first();
  }

  public function create(array $data)
  {
    return Keyword::firstOrCreate($data);
  }

  public function update(int $id, array $data)
  {
    $keyword = $this->findOne($id);
    
    if(!$keyword){
      return $keyword;
    }

    return $keyword->update($data);
  }

  public function delete(int $id)
  {
    $keyword = $this->findOne($id);

    if(!$keyword){
      return $keyword;
    }

    return $keyword->delete();
  }
}