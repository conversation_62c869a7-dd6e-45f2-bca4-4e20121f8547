<?php

namespace App\Repositories;

use App\Models\LogActivity;
use App\Repositories\Contracts\LogRepositoryInterface;
use Carbon\Carbon;

class LogRepository implements LogRepositoryInterface
{
  public function log(string $userId, ?string $contentId, string $activity, ?string $route, string $icon)
  {
    return LogActivity::create([
        'user_id' => $userId,
        'content_id' => $contentId,
        'activity' => $activity,
        'route' => $route,
        'icon' => $icon,
    ]);
  }

  public function getYears(?string $userId)
  {
    return LogActivity::selectRaw('YEAR(created_at) as year')
    ->when($userId, fn($query) => $query->where('user_id', $userId))
    ->whereYear('created_at', '>=', Carbon::now()->subYears(2)->year)
    ->whereYear('created_at', '<=', Carbon::now()->year)
    ->distinct()
    ->pluck('year');
  }

  public function getActivities($year, ?string $userId)
  {
    $logs = LogActivity::with(['content.author', 'user'])
      ->whereYear('created_at', $year)
      ->when($userId, fn($query) => $query->where('user_id', $userId))
      ->latest()
      ->paginate(10)
      ->appends(['year' => $year]);

    return response()->json($logs);
  }

  public function getRecentActivity(string $userId)
  {
    $logs = LogActivity::with(['content.author'])
      ->where('user_id', $userId)
      ->latest()
      ->limit(5)
      ->get();

    return $logs;
  }
}