<?php 

namespace App\Repositories;

use App\Models\Notification;
use App\Repositories\Contracts\NotificationRepositoryInterface;
use Carbon\Carbon;

class NotificationRepository implements NotificationRepositoryInterface
{
  public function getNotifications(string $userId)
  {
    return Notification::with('user')
      ->where('to', $userId)
      ->where('is_read', 0)
      // ->where('created_at', '>=', Carbon::now()->subDays(14))
      ->latest()
      ->limit(10)
      ->get();
  }

  public function create(string $to, string $from, string $type, string $message)
  {
    return Notification::create([
      'to' => $to,
      'from' => $from,
      'type' => $type,
      'message' => $message,
      'is_read' => false,
    ]);
  }

  public function readNotification(int $notifId)
  {
    $notif = Notification::findOrFail($notifId);

    if(!$notif){
      return $notif;
    }

    return $notif->update(['is_read' => 1]);
  }

  public function readAllNotification(array $notifIds)
  {
    $update = Notification::whereIn('id', $notifIds)->update(['is_read' => 1]);

    if(!$update){
      return $update;
    }

    return $update;
  }
}