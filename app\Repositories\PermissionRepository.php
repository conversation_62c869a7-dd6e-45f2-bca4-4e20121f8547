<?php

namespace App\Repositories;

use App\Models\Permission;
use App\Repositories\Contracts\PermissionRepositoryInterface;

class PermissionRepository implements PermissionRepositoryInterface {
  public function getPermission($type)
  {
    if($type == 'all'){
      return Permission::all();
    }else{
      // Fix for PostgreSQL - select only the columns we need and use proper grouping
      return Permission::select('group_by')
        ->distinct()
        ->orderBy('group_by')
        ->get();
    }
  }

  public function getPermissionGroup(int $groupBy)
  {
    return Permission::where('group_by', '=', $groupBy)->get();
  }
}