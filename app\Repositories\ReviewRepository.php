<?php

namespace App\Repositories;

use App\Models\Content;
use App\Models\ContentReview;
use App\Repositories\Contracts\ReviewRepositoryInterface;

class ReviewRepository implements ReviewRepositoryInterface
{
  public function findOneContent(string $id)
  {
    return Content::findOrFail($id);
  }

  public function getReviews(string $contentId)
  {
    return ContentReview::with('reviewer:id,name')
    ->select('id', 'reviewer_id', 'status', 'comments', 'review_date')
    ->where('content_id', $contentId)
    ->orderBy('review_date', 'desc')
    ->get();
  }

  public function create(array $data)
  {
    return ContentReview::create($data);
  }

  public function approval(array $data)
  {
    $content = $this->findOneContent($data['id']);

    if(!$content) return $content;

    return $content->update($data);
  }

  public function publication(array $data)
  {
    $content = $this->findOneContent($data['id']);

    if(!$content) return $content;

    return $content->update($data);
  }
}