<?php

namespace App\Repositories;

use App\Models\RolePermission;
use App\Repositories\Contracts\RolePermissionRepositoryInterface;

class RolePermissionRepository implements RolePermissionRepositoryInterface {
  public function getRolePermissions(int $id)
  {
    return RolePermission::where('role_id', $id)->get();
  }
  
  public function updateOrCreate(int $role_id, int $permission_id)
  {
    return RolePermission::updateorcreate([
      'role_id' => $role_id,
      'permission_id' => $permission_id
    ]);
  }

  public function deleteRolePermissions(int $id)
  {
    return RolePermission::where('role_id', $id)->delete();
  }
}