<?php

namespace App\Repositories;

use App\Models\Permission;
use App\Models\Role;
use App\Repositories\Contracts\RoleRepositoryInterface;

class RoleRepository implements RoleRepositoryInterface {
    public function getRoles(string $type)
    {
      if($type == "all"){
        return Role::all();
      }else{
        return Role::paginate(5);
      }
    }
  
    public function findOne(int $id)
    {
      return Role::find($id);
    }

    public function create($data)
    {
      return Role::create($data);
    }

    public function update(int $id, array $data)
    {
      $role = Role::find($id);

      if(!$role){
        return false;
      }

      return $role->update($data);
    }

    public function delete(int $id)
    {
      $role = Role::find($id);

      if(!$role){
        return false;
      }
      
      return $role->delete($id);
    }
}