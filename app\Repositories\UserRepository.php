<?php

namespace App\Repositories;

use App\Models\Role;
use App\Models\User;
use App\Repositories\Contracts\UserRepositoryInterface;

class UserRepository implements UserRepositoryInterface {
    public function getAll()
    {
      return User::with('role')->paginate(10);
    }
  
    public function findOne(string $id)
    {
      return User::with('role')->where('id', $id)->first();
    }

    public function create($data)
    {
      return User::create($data);
    }

    public function update(string $id, array $data): bool
    {
      $user = User::find($id);

      if(!$user){
        return false;
      }

      return $user->update($data);
    }

    public function delete(string $id): bool
    {
      $user = User::find($id);

      if(!$user){
        return false;
      }
      
      return $user->delete($id);
    }
}