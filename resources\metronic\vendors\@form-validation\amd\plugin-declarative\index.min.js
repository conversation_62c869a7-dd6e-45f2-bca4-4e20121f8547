/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-declarative
 * @version 2.4.0
 */

define(["exports","@form-validation/core"],(function(e,t){"use strict";var a=function(e,t){return a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a])},a(e,t)};var n=function(e){function t(t){var a=e.call(this,t)||this;return a.addedFields=new Map,a.opts=Object.assign({},{html5Input:!1,pluginPrefix:"data-fvp-",prefix:"data-fv-"},t),a.fieldAddedHandler=a.onFieldAdded.bind(a),a.fieldRemovedHandler=a.onFieldRemoved.bind(a),a}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}a(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}(t,e),t.prototype.install=function(){var e=this;this.parsePlugins();var t=this.parseOptions();Object.keys(t).forEach((function(a){e.addedFields.has(a)||e.addedFields.set(a,!0),e.core.addField(a,t[a])})),this.core.on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler)},t.prototype.uninstall=function(){this.addedFields.clear(),this.core.off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler)},t.prototype.onFieldAdded=function(e){var t=this,a=e.elements;a&&0!==a.length&&!this.addedFields.has(e.field)&&(this.addedFields.set(e.field,!0),a.forEach((function(a){var n=t.parseElement(a);if(!t.isEmptyOption(n)){var r={selector:e.options.selector,validators:Object.assign({},e.options.validators||{},n.validators)};t.core.setFieldOptions(e.field,r)}})))},t.prototype.onFieldRemoved=function(e){e.field&&this.addedFields.has(e.field)&&this.addedFields.delete(e.field)},t.prototype.parseOptions=function(){var e=this,t=this.opts.prefix,a={},n=this.core.getFields(),r=this.core.getFormElement();return[].slice.call(r.querySelectorAll("[name], [".concat(t,"field]"))).forEach((function(n){var r=e.parseElement(n);if(!e.isEmptyOption(r)){var i=n.getAttribute("name")||n.getAttribute("".concat(t,"field"));a[i]=Object.assign({},a[i],r)}})),Object.keys(a).forEach((function(e){Object.keys(a[e].validators).forEach((function(t){a[e].validators[t].enabled=a[e].validators[t].enabled||!1,n[e]&&n[e].validators&&n[e].validators[t]&&Object.assign(a[e].validators[t],n[e].validators[t])}))})),Object.assign({},n,a)},t.prototype.createPluginInstance=function(e,t){for(var a=e.split("."),n=window||this,r=0,i=a.length;r<i;r++)n=n[a[r]];if("function"!=typeof n)throw new Error("the plugin ".concat(e," doesn't exist"));return new n(t)},t.prototype.parsePlugins=function(){for(var e,t=this,a=this.core.getFormElement(),n=new RegExp("^".concat(this.opts.pluginPrefix,"([a-z0-9-]+)(___)*([a-z0-9-]+)*$")),r=a.attributes.length,i={},s=0;s<r;s++){var o=a.attributes[s].name,l=a.attributes[s].value,d=n.exec(o);if(d&&4===d.length){var c=this.toCamelCase(d[1]);i[c]=Object.assign({},d[3]?((e={})[this.toCamelCase(d[3])]=l,e):{enabled:""===l||"true"===l},i[c])}}Object.keys(i).forEach((function(e){var a=i[e],n=a.enabled,r=a.class;if(n&&r){delete a.enabled,delete a.clazz;var s=t.createPluginInstance(r,a);t.core.registerPlugin(e,s)}}))},t.prototype.isEmptyOption=function(e){var t=e.validators;return 0===Object.keys(t).length&&t.constructor===Object},t.prototype.parseElement=function(e){for(var t=new RegExp("^".concat(this.opts.prefix,"([a-z0-9-]+)(___)*([a-z0-9-]+)*$")),a=e.attributes.length,n={},r=e.getAttribute("type"),i=0;i<a;i++){var s=e.attributes[i].name,o=e.attributes[i].value;if(this.opts.html5Input)switch(!0){case"minlength"===s:n.stringLength=Object.assign({},{enabled:!0,min:parseInt(o,10)},n.stringLength);break;case"maxlength"===s:n.stringLength=Object.assign({},{enabled:!0,max:parseInt(o,10)},n.stringLength);break;case"pattern"===s:n.regexp=Object.assign({},{enabled:!0,regexp:o},n.regexp);break;case"required"===s:n.notEmpty=Object.assign({},{enabled:!0},n.notEmpty);break;case"type"===s&&"color"===o:n.color=Object.assign({},{enabled:!0,type:"hex"},n.color);break;case"type"===s&&"email"===o:n.emailAddress=Object.assign({},{enabled:!0},n.emailAddress);break;case"type"===s&&"url"===o:n.uri=Object.assign({},{enabled:!0},n.uri);break;case"type"===s&&"range"===o:n.between=Object.assign({},{enabled:!0,max:parseFloat(e.getAttribute("max")),min:parseFloat(e.getAttribute("min"))},n.between);break;case"min"===s&&"date"!==r&&"range"!==r:n.greaterThan=Object.assign({},{enabled:!0,min:parseFloat(o)},n.greaterThan);break;case"max"===s&&"date"!==r&&"range"!==r:n.lessThan=Object.assign({},{enabled:!0,max:parseFloat(o)},n.lessThan)}var l=t.exec(s);if(l&&4===l.length){var d=this.toCamelCase(l[1]);n[d]||(n[d]={}),l[3]?n[d][this.toCamelCase(l[3])]=this.normalizeValue(o):!0===n[d].enabled&&!1===n[d].enabled||(n[d].enabled=""===o||"true"===o)}}return{validators:n}},t.prototype.normalizeValue=function(e){return"true"===e||""===e||"false"!==e&&e},t.prototype.toUpperCase=function(e){return e.charAt(1).toUpperCase()},t.prototype.toCamelCase=function(e){return e.replace(/-./g,this.toUpperCase)},t}(t.Plugin);e.Declarative=n}));
