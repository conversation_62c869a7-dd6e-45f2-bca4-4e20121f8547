/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-field-status
 * @version 2.4.0
 */

define(["exports","@form-validation/core"],(function(e,t){"use strict";var n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},n(e,t)};var d=function(e){function t(t){var n=e.call(this,t)||this;return n.statuses=new Map,n.opts=Object.assign({},{onStatusChanged:function(){}},t),n.elementValidatingHandler=n.onElementValidating.bind(n),n.elementValidatedHandler=n.onElementValidated.bind(n),n.elementNotValidatedHandler=n.onElementNotValidated.bind(n),n.elementIgnoredHandler=n.onElementIgnored.bind(n),n.fieldAddedHandler=n.onFieldAdded.bind(n),n.fieldRemovedHandler=n.onFieldRemoved.bind(n),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function d(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(d.prototype=t.prototype,new d)}(t,e),t.prototype.install=function(){this.core.on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.ignored",this.elementIgnoredHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler)},t.prototype.uninstall=function(){this.statuses.clear(),this.core.off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.ignored",this.elementIgnoredHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler)},t.prototype.areFieldsValid=function(){return Array.from(this.statuses.values()).every((function(e){return"Valid"===e||"NotValidated"===e||"Ignored"===e}))},t.prototype.getStatuses=function(){return this.isEnabled?this.statuses:new Map},t.prototype.onFieldAdded=function(e){this.statuses.set(e.field,"NotValidated")},t.prototype.onFieldRemoved=function(e){this.statuses.has(e.field)&&this.statuses.delete(e.field),this.handleStatusChanged(this.areFieldsValid())},t.prototype.onElementValidating=function(e){this.statuses.set(e.field,"Validating"),this.handleStatusChanged(!1)},t.prototype.onElementValidated=function(e){this.statuses.set(e.field,e.valid?"Valid":"Invalid"),e.valid?this.handleStatusChanged(this.areFieldsValid()):this.handleStatusChanged(!1)},t.prototype.onElementNotValidated=function(e){this.statuses.set(e.field,"NotValidated"),this.handleStatusChanged(!1)},t.prototype.onElementIgnored=function(e){this.statuses.set(e.field,"Ignored"),this.handleStatusChanged(this.areFieldsValid())},t.prototype.handleStatusChanged=function(e){this.isEnabled&&this.opts.onStatusChanged(e)},t}(t.Plugin);e.FieldStatus=d}));
