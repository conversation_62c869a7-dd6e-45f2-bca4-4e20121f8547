/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-framework
 * @version 2.4.0
 */

define(["exports","@form-validation/core","@form-validation/plugin-message"],(function(e,t,o){"use strict";var n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])},n(e,t)};var s=t.utils.classSet,i=t.utils.closest,a=function(e){function t(t){var o=e.call(this,t)||this;return o.results=new Map,o.containers=new Map,o.opts=Object.assign({},{defaultMessageContainer:!0,eleInvalidClass:"",eleValidClass:"",rowClasses:"",rowValidatingClass:""},t),o.elementIgnoredHandler=o.onElementIgnored.bind(o),o.elementValidatingHandler=o.onElementValidating.bind(o),o.elementValidatedHandler=o.onElementValidated.bind(o),o.elementNotValidatedHandler=o.onElementNotValidated.bind(o),o.iconPlacedHandler=o.onIconPlaced.bind(o),o.fieldAddedHandler=o.onFieldAdded.bind(o),o.fieldRemovedHandler=o.onFieldRemoved.bind(o),o.messagePlacedHandler=o.onMessagePlaced.bind(o),o}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function o(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(o.prototype=t.prototype,new o)}(t,e),t.prototype.install=function(){var e,n=this;s(this.core.getFormElement(),((e={})[this.opts.formClass]=!0,e["fv-plugins-framework"]=!0,e)),this.core.on("core.element.ignored",this.elementIgnoredHandler).on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("plugins.icon.placed",this.iconPlacedHandler).on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler),this.opts.defaultMessageContainer&&(this.core.registerPlugin(t.MESSAGE_PLUGIN,new o.Message({clazz:this.opts.messageClass,container:function(e,t){var s="string"==typeof n.opts.rowSelector?n.opts.rowSelector:n.opts.rowSelector(e,t),a=i(t,s);return o.Message.getClosestContainer(t,a,n.opts.rowPattern)}})),this.core.on("plugins.message.placed",this.messagePlacedHandler))},t.prototype.uninstall=function(){var e;this.results.clear(),this.containers.clear(),s(this.core.getFormElement(),((e={})[this.opts.formClass]=!1,e["fv-plugins-framework"]=!1,e)),this.core.off("core.element.ignored",this.elementIgnoredHandler).off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("plugins.icon.placed",this.iconPlacedHandler).off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler),this.opts.defaultMessageContainer&&(this.core.deregisterPlugin(t.MESSAGE_PLUGIN),this.core.off("plugins.message.placed",this.messagePlacedHandler))},t.prototype.onEnabled=function(){var e;s(this.core.getFormElement(),((e={})[this.opts.formClass]=!0,e)),this.opts.defaultMessageContainer&&this.core.enablePlugin(t.MESSAGE_PLUGIN)},t.prototype.onDisabled=function(){var e;s(this.core.getFormElement(),((e={})[this.opts.formClass]=!1,e)),this.opts.defaultMessageContainer&&this.core.disablePlugin(t.MESSAGE_PLUGIN)},t.prototype.onIconPlaced=function(e){},t.prototype.onMessagePlaced=function(e){},t.prototype.onFieldAdded=function(e){var t=this,o=e.elements;o&&(o.forEach((function(e){var o,n=t.containers.get(e);n&&(s(n,((o={})[t.opts.rowInvalidClass]=!1,o[t.opts.rowValidatingClass]=!1,o[t.opts.rowValidClass]=!1,o["fv-plugins-icon-container"]=!1,o)),t.containers.delete(e))})),this.prepareFieldContainer(e.field,o))},t.prototype.onFieldRemoved=function(e){var t=this;e.elements.forEach((function(e){var o,n=t.containers.get(e);n&&s(n,((o={})[t.opts.rowInvalidClass]=!1,o[t.opts.rowValidatingClass]=!1,o[t.opts.rowValidClass]=!1,o))}))},t.prototype.prepareFieldContainer=function(e,t){var o=this;if(t.length){var n=t[0].getAttribute("type");"radio"===n||"checkbox"===n?this.prepareElementContainer(e,t[0]):t.forEach((function(t){return o.prepareElementContainer(e,t)}))}},t.prototype.prepareElementContainer=function(e,t){var o,n="string"==typeof this.opts.rowSelector?this.opts.rowSelector:this.opts.rowSelector(e,t),a=i(t,n);a!==t&&(s(a,((o={})[this.opts.rowClasses]=!0,o["fv-plugins-icon-container"]=!0,o)),this.containers.set(t,a))},t.prototype.onElementValidating=function(e){this.removeClasses(e.element,e.elements)},t.prototype.onElementNotValidated=function(e){this.removeClasses(e.element,e.elements)},t.prototype.onElementIgnored=function(e){this.removeClasses(e.element,e.elements)},t.prototype.removeClasses=function(e,t){var o,n=this,i=e.getAttribute("type"),a="radio"===i||"checkbox"===i?t[0]:e;t.forEach((function(e){var t;s(e,((t={})[n.opts.eleValidClass]=!1,t[n.opts.eleInvalidClass]=!1,t))}));var l=this.containers.get(a);l&&s(l,((o={})[this.opts.rowInvalidClass]=!1,o[this.opts.rowValidatingClass]=!1,o[this.opts.rowValidClass]=!1,o))},t.prototype.onElementValidated=function(e){var t,o,n=this,i=e.elements,a=e.element.getAttribute("type"),l="radio"===a||"checkbox"===a?i[0]:e.element;i.forEach((function(t){var o;s(t,((o={})[n.opts.eleValidClass]=e.valid,o[n.opts.eleInvalidClass]=!e.valid,o))}));var r=this.containers.get(l);if(r)if(e.valid){this.results.delete(l);var d=!0;this.containers.forEach((function(e,t){e===r&&!1===n.results.get(t)&&(d=!1)})),d&&s(r,((o={})[this.opts.rowInvalidClass]=!1,o[this.opts.rowValidatingClass]=!1,o[this.opts.rowValidClass]=!0,o))}else this.results.set(l,!1),s(r,((t={})[this.opts.rowInvalidClass]=!0,t[this.opts.rowValidatingClass]=!1,t[this.opts.rowValidClass]=!1,t))},t.MESSAGE_PLUGIN="___frameworkMessage",t}(t.Plugin);e.Framework=a}));
