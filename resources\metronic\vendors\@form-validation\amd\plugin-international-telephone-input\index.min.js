/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-international-telephone-input
 * @version 2.4.0
 */

define(["exports","@form-validation/core"],(function(e,t){"use strict";var n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},n(e,t)};var i=function(e){function t(t){var n=e.call(this,t)||this;return n.intlTelInstances=new Map,n.countryChangeHandler=new Map,n.fieldElements=new Map,n.hiddenFieldElements=new Map,n.opts=Object.assign({},{autoPlaceholder:"polite",utilsScript:""},t),n.validatePhoneNumber=n.checkPhoneNumber.bind(n),n.fields="string"==typeof n.opts.field?n.opts.field.split(","):n.opts.field,n.hiddenFieldInputs=n.opts.hiddenPhoneInput?"string"==typeof n.opts.hiddenPhoneInput?n.opts.hiddenPhoneInput.split(","):n.opts.hiddenPhoneInput:[],n.onValidatorValidatedHandler=n.onValidatorValidated.bind(n),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}(t,e),t.prototype.install=function(){var e=this;this.core.registerValidator(t.INT_TEL_VALIDATOR,this.validatePhoneNumber);var n=this.hiddenFieldInputs.length;this.fields.forEach((function(i,o){var a;e.core.addField(i,{validators:(a={},a[t.INT_TEL_VALIDATOR]={message:e.opts.message},a)});var l=e.core.getElements(i)[0],r=function(){return e.core.revalidateField(i)};if(l.addEventListener("countrychange",r),e.countryChangeHandler.set(i,r),e.fieldElements.set(i,l),e.intlTelInstances.set(i,intlTelInput(l,e.opts)),o<n&&e.hiddenFieldInputs[o]){var d=document.createElement("input");d.setAttribute("type","hidden"),d.setAttribute("name",e.hiddenFieldInputs[o]),e.core.getFormElement().appendChild(d),e.hiddenFieldElements.set(i,d)}})),n>0&&this.core.on("core.validator.validated",this.onValidatorValidatedHandler)},t.prototype.uninstall=function(){var e=this,n=this.hiddenFieldInputs.length;this.fields.forEach((function(i,o){var a=e.countryChangeHandler.get(i),l=e.fieldElements.get(i),r=e.getIntTelInstance(i);if(a&&l&&r&&(l.removeEventListener("countrychange",a),e.core.disableValidator(i,t.INT_TEL_VALIDATOR),r.destroy()),o<n&&e.hiddenFieldInputs[o]){var d=e.hiddenFieldElements.get(i);d&&e.core.getFormElement().removeChild(d)}})),n>0&&this.core.off("core.validator.validated",this.onValidatorValidatedHandler),this.fieldElements.clear(),this.hiddenFieldElements.clear()},t.prototype.getIntTelInstance=function(e){return this.intlTelInstances.get(e)},t.prototype.onEnabled=function(){var e=this;this.fields.forEach((function(n){e.core.enableValidator(n,t.INT_TEL_VALIDATOR)}))},t.prototype.onDisabled=function(){var e=this;this.fields.forEach((function(n){e.core.disableValidator(n,t.INT_TEL_VALIDATOR);var i=e.hiddenFieldElements.get(n);i&&(i.value="")}))},t.prototype.checkPhoneNumber=function(){var e=this;return{validate:function(t){var n=t.value,i=e.getIntTelInstance(t.field);return""!==n&&i?{valid:i.isValidNumber()}:{valid:!0}}}},t.prototype.onValidatorValidated=function(e){if(0!==this.hiddenFieldInputs.length&&e.validator===t.INT_TEL_VALIDATOR){var n=e.field,i=this.hiddenFieldElements.get(n);if(i)if(this.isEnabled&&e.result.valid){var o=this.getIntTelInstance(n).getNumber();i.value=o}else i.value=""}},t.INT_TEL_VALIDATOR="___InternationalTelephoneInputValidator",t}(t.Plugin);e.InternationalTelephoneInput=i}));
