/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-mandatory-icon
 * @version 2.4.0
 */

define(["exports","@form-validation/core"],(function(t,e){"use strict";var n=function(t,e){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},n(t,e)};var i=e.utils.classSet,o=function(t){function e(e){var n=t.call(this,e)||this;return n.removedIcons={Invalid:"",NotValidated:"",Valid:"",Validating:""},n.icons=new Map,n.elementValidatingHandler=n.onElementValidating.bind(n),n.elementValidatedHandler=n.onElementValidated.bind(n),n.elementNotValidatedHandler=n.onElementNotValidated.bind(n),n.iconPlacedHandler=n.onIconPlaced.bind(n),n.iconSetHandler=n.onIconSet.bind(n),n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}(e,t),e.prototype.install=function(){this.core.on("core.element.validating",this.elementValidatingHandler).on("core.element.validated",this.elementValidatedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("plugins.icon.placed",this.iconPlacedHandler).on("plugins.icon.set",this.iconSetHandler)},e.prototype.uninstall=function(){this.icons.clear(),this.core.off("core.element.validating",this.elementValidatingHandler).off("core.element.validated",this.elementValidatedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("plugins.icon.placed",this.iconPlacedHandler).off("plugins.icon.set",this.iconSetHandler)},e.prototype.onEnabled=function(){var t=this;this.icons.forEach((function(e,n,o){var a;i(n,((a={})[t.opts.icon]=!0,a))}))},e.prototype.onDisabled=function(){var t=this;this.icons.forEach((function(e,n,o){var a;i(n,((a={})[t.opts.icon]=!1,a))}))},e.prototype.onIconPlaced=function(t){var e,n=this,o=this.core.getFields()[t.field].validators,a=this.core.getElements(t.field);if(o&&o.notEmpty&&!1!==o.notEmpty.enabled&&a.length){this.icons.set(t.element,t.iconElement);for(var l=a[0].getAttribute("type"),s=l?l.toLowerCase():"",d=0,c="checkbox"===s||"radio"===s?[a[0]]:a;d<c.length;d++){var r=c[d];""===this.core.getElementValue(t.field,r)&&i(t.iconElement,((e={})[this.opts.icon]=this.isEnabled,e))}}this.iconClasses=t.classes;var p=this.opts.icon.split(" "),h={Invalid:this.iconClasses.invalid?this.iconClasses.invalid.split(" "):[],Valid:this.iconClasses.valid?this.iconClasses.valid.split(" "):[],Validating:this.iconClasses.validating?this.iconClasses.validating.split(" "):[]};Object.keys(h).forEach((function(t){for(var e=[],i=0,o=p;i<o.length;i++){var a=o[i];-1===h[t].indexOf(a)&&e.push(a)}n.removedIcons[t]=e.join(" ")}))},e.prototype.onElementValidating=function(t){this.updateIconClasses(t.element,"Validating")},e.prototype.onElementValidated=function(t){this.updateIconClasses(t.element,t.valid?"Valid":"Invalid")},e.prototype.onElementNotValidated=function(t){this.updateIconClasses(t.element,"NotValidated")},e.prototype.updateIconClasses=function(t,e){var n,o=this.icons.get(t);o&&this.iconClasses&&(this.iconClasses.valid||this.iconClasses.invalid||this.iconClasses.validating)&&i(o,((n={})[this.removedIcons[e]]=!1,n[this.opts.icon]=!1,n))},e.prototype.onIconSet=function(t){var e,n=this.icons.get(t.element);n&&("NotValidated"===t.status&&""===this.core.getElementValue(t.field,t.element)||"Ignored"===t.status)&&i(n,((e={})[this.opts.icon]=this.isEnabled,e))},e}(e.Plugin);t.MandatoryIcon=o}));
