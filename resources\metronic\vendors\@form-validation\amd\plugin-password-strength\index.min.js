/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-password-strength
 * @version 2.4.0
 */

define(["exports","@form-validation/core"],(function(t,e){"use strict";var a=function(t,e){return a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a])},a(t,e)};var o=function(t){function e(e){var a=t.call(this,e)||this;return a.opts=Object.assign({},{minimalScore:3,onValidated:function(){}},e),a.validatePassword=a.checkPasswordStrength.bind(a),a.validatorValidatedHandler=a.onValidatorValidated.bind(a),a}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}a(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}(e,t),e.prototype.install=function(){var t;this.core.registerValidator(e.PASSWORD_STRENGTH_VALIDATOR,this.validatePassword),this.core.on("core.validator.validated",this.validatorValidatedHandler),this.core.addField(this.opts.field,{validators:(t={},t[e.PASSWORD_STRENGTH_VALIDATOR]={message:this.opts.message,minimalScore:this.opts.minimalScore},t)})},e.prototype.uninstall=function(){this.core.off("core.validator.validated",this.validatorValidatedHandler),this.core.disableValidator(this.opts.field,e.PASSWORD_STRENGTH_VALIDATOR)},e.prototype.onEnabled=function(){this.core.enableValidator(this.opts.field,e.PASSWORD_STRENGTH_VALIDATOR)},e.prototype.onDisabled=function(){this.core.disableValidator(this.opts.field,e.PASSWORD_STRENGTH_VALIDATOR)},e.prototype.checkPasswordStrength=function(){var t=this;return{validate:function(e){var a=e.value;if(""===a)return{valid:!0};var o=zxcvbn(a),i=o.score,r=o.feedback.warning||"The password is weak";return i<t.opts.minimalScore?{message:r,meta:{message:r,score:i},valid:!1}:{meta:{message:r,score:i},valid:!0}}}},e.prototype.onValidatorValidated=function(t){if(this.isEnabled&&t.field===this.opts.field&&t.validator===e.PASSWORD_STRENGTH_VALIDATOR&&t.result.meta){var a=t.result.meta.message,o=t.result.meta.score;this.opts.onValidated(t.result.valid,a,o)}},e.PASSWORD_STRENGTH_VALIDATOR="___PasswordStrengthValidator",e}(e.Plugin);t.PasswordStrength=o}));
