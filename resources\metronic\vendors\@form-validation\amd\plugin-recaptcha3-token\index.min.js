/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-recaptcha3-token
 * @version 2.4.0
 */

define(["exports","@form-validation/core"],(function(t,e){"use strict";var o=function(t,e){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])},o(t,e)};var n=function(t){function e(e){var o=t.call(this,e)||this;return o.opts=Object.assign({},{action:"submit",hiddenTokenName:"___hidden-token___"},e),o.onValidHandler=o.onFormValid.bind(o),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}(e,t),e.prototype.install=function(){this.core.on("core.form.valid",this.onValidHandler),this.hiddenTokenEle=document.createElement("input"),this.hiddenTokenEle.setAttribute("type","hidden"),this.core.getFormElement().appendChild(this.hiddenTokenEle);var t=void 0===window[e.LOADED_CALLBACK]?function(){}:window[e.LOADED_CALLBACK];window[e.LOADED_CALLBACK]=function(){t()};var o=this.getScript();if(!document.body.querySelector('script[src="'.concat(o,'"]'))){var n=document.createElement("script");n.type="text/javascript",n.async=!0,n.defer=!0,n.src=o,document.body.appendChild(n)}},e.prototype.uninstall=function(){delete window[e.LOADED_CALLBACK],this.core.off("core.form.valid",this.onValidHandler);var t=this.getScript();[].slice.call(document.body.querySelectorAll('script[src="'.concat(t,'"]'))).forEach((function(t){return t.parentNode.removeChild(t)})),this.core.getFormElement().removeChild(this.hiddenTokenEle)},e.prototype.onFormValid=function(){var t=this;this.isEnabled&&window.grecaptcha.execute(this.opts.siteKey,{action:this.opts.action}).then((function(e){t.hiddenTokenEle.setAttribute("name",t.opts.hiddenTokenName),t.hiddenTokenEle.value=e;var o=t.core.getFormElement();o instanceof HTMLFormElement&&o.submit()}))},e.prototype.getScript=function(){var t=this.opts.language?"&hl=".concat(this.opts.language):"";return"https://www.google.com/recaptcha/api.js?"+"onload=".concat(e.LOADED_CALLBACK,"&render=").concat(this.opts.siteKey).concat(t)},e.LOADED_CALLBACK="___reCaptcha3TokenLoaded___",e}(e.Plugin);t.Recaptcha3Token=n}));
