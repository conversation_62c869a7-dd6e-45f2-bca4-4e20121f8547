/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-recaptcha3
 * @version 2.4.0
 */

define(["exports","@form-validation/core"],(function(t,e){"use strict";var o=function(t,e){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])},o(t,e)};var n=e.utils.fetch,c=e.utils.removeUndefined,i=function(t){function e(e){var o=t.call(this,e)||this;return o.opts=Object.assign({},{minimumScore:0},c(e)),o.iconPlacedHandler=o.onIconPlaced.bind(o),o}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}(e,t),e.prototype.install=function(){var t=this;this.core.on("plugins.icon.placed",this.iconPlacedHandler);var o=void 0===window[e.LOADED_CALLBACK]?function(){}:window[e.LOADED_CALLBACK];window[e.LOADED_CALLBACK]=function(){o();var c=document.createElement("input");c.setAttribute("type","hidden"),c.setAttribute("name",e.CAPTCHA_FIELD),document.getElementById(t.opts.element).appendChild(c),t.core.addField(e.CAPTCHA_FIELD,{validators:{promise:{message:t.opts.message,promise:function(o){return new Promise((function(o,c){window.grecaptcha.execute(t.opts.siteKey,{action:t.opts.action}).then((function(i){var r;n(t.opts.backendVerificationUrl,{method:"POST",params:(r={},r[e.CAPTCHA_FIELD]=i,r)}).then((function(e){var n="true"==="".concat(e.success)&&e.score>=t.opts.minimumScore;o({message:e.message||t.opts.message,meta:e,valid:n})})).catch((function(t){c({valid:!1})}))}))}))}}}})};var c=this.getScript();if(!document.body.querySelector('script[src="'.concat(c,'"]'))){var i=document.createElement("script");i.type="text/javascript",i.async=!0,i.defer=!0,i.src=c,document.body.appendChild(i)}},e.prototype.uninstall=function(){delete window[e.LOADED_CALLBACK],this.core.off("plugins.icon.placed",this.iconPlacedHandler);var t=this.getScript();[].slice.call(document.body.querySelectorAll('script[src="'.concat(t,'"]'))).forEach((function(t){return t.parentNode.removeChild(t)})),this.core.removeField(e.CAPTCHA_FIELD)},e.prototype.onEnabled=function(){this.core.enableValidator(e.CAPTCHA_FIELD,"promise")},e.prototype.onDisabled=function(){this.core.disableValidator(e.CAPTCHA_FIELD,"promise")},e.prototype.getScript=function(){var t=this.opts.language?"&hl=".concat(this.opts.language):"";return"https://www.google.com/recaptcha/api.js?"+"onload=".concat(e.LOADED_CALLBACK,"&render=").concat(this.opts.siteKey).concat(t)},e.prototype.onIconPlaced=function(t){t.field===e.CAPTCHA_FIELD&&(t.iconElement.style.display="none")},e.CAPTCHA_FIELD="___g-recaptcha-token___",e.LOADED_CALLBACK="___reCaptcha3Loaded___",e}(e.Plugin);t.Recaptcha3=i}));
