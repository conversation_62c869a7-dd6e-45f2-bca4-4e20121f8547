/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-recaptcha
 * @version 2.4.0
 */

define(["exports","@form-validation/core"],(function(e,t){"use strict";var i=function(e,t){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},i(e,t)};var o=t.utils.fetch,a=t.utils.removeUndefined,r=function(e){function t(i){var o=e.call(this,i)||this;return o.widgetIds=new Map,o.captchaStatus="NotValidated",o.opts=Object.assign({},t.DEFAULT_OPTIONS,a(i)),o.fieldResetHandler=o.onResetField.bind(o),o.preValidateFilter=o.preValidate.bind(o),o.iconPlacedHandler=o.onIconPlaced.bind(o),o}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function o(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(o.prototype=t.prototype,new o)}(t,e),t.prototype.install=function(){var e=this;this.core.on("core.field.reset",this.fieldResetHandler).on("plugins.icon.placed",this.iconPlacedHandler).registerFilter("validate-pre",this.preValidateFilter);var i=void 0===window[t.LOADED_CALLBACK]?function(){}:window[t.LOADED_CALLBACK];window[t.LOADED_CALLBACK]=function(){i();var a={badge:e.opts.badge,callback:function(){""===e.opts.backendVerificationUrl&&(e.captchaStatus="Valid",e.core.updateFieldStatus(t.CAPTCHA_FIELD,"Valid"))},"error-callback":function(){e.captchaStatus="Invalid",e.core.updateFieldStatus(t.CAPTCHA_FIELD,"Invalid")},"expired-callback":function(){e.captchaStatus="NotValidated",e.core.updateFieldStatus(t.CAPTCHA_FIELD,"NotValidated")},sitekey:e.opts.siteKey,size:e.opts.size},r=window.grecaptcha.render(e.opts.element,a);e.widgetIds.set(e.opts.element,r),e.core.addField(t.CAPTCHA_FIELD,{validators:{promise:{message:e.opts.message,promise:function(i){var a,r=e.widgetIds.has(e.opts.element)?window.grecaptcha.getResponse(e.widgetIds.get(e.opts.element)):i.value;return""===r?(e.captchaStatus="Invalid",Promise.resolve({valid:!1})):""===e.opts.backendVerificationUrl?(e.captchaStatus="Valid",Promise.resolve({valid:!0})):"Valid"===e.captchaStatus?Promise.resolve({valid:!0}):o(e.opts.backendVerificationUrl,{method:"POST",params:(a={},a[t.CAPTCHA_FIELD]=r,a)}).then((function(t){var i="true"==="".concat(t.success);return e.captchaStatus=i?"Valid":"Invalid",Promise.resolve({meta:t,valid:i})})).catch((function(t){return e.captchaStatus="NotValidated",Promise.reject({valid:!1})}))}}}})};var a=this.getScript();if(!document.body.querySelector('script[src="'.concat(a,'"]'))){var r=document.createElement("script");r.type="text/javascript",r.async=!0,r.defer=!0,r.src=a,document.body.appendChild(r)}},t.prototype.uninstall=function(){delete window[t.LOADED_CALLBACK],this.timer&&clearTimeout(this.timer),this.core.off("core.field.reset",this.fieldResetHandler).off("plugins.icon.placed",this.iconPlacedHandler).deregisterFilter("validate-pre",this.preValidateFilter),this.widgetIds.clear();var e=this.getScript();[].slice.call(document.body.querySelectorAll('script[src="'.concat(e,'"]'))).forEach((function(e){return e.parentNode.removeChild(e)})),this.core.removeField(t.CAPTCHA_FIELD)},t.prototype.onEnabled=function(){this.core.enableValidator(t.CAPTCHA_FIELD,"promise")},t.prototype.onDisabled=function(){this.core.disableValidator(t.CAPTCHA_FIELD,"promise")},t.prototype.getScript=function(){var e=this.opts.language?"&hl=".concat(this.opts.language):"";return"https://www.google.com/recaptcha/api.js?onload=".concat(t.LOADED_CALLBACK,"&render=explicit").concat(e)},t.prototype.preValidate=function(){var e=this;if(this.isEnabled&&"invisible"===this.opts.size&&this.widgetIds.has(this.opts.element)){var t=this.widgetIds.get(this.opts.element);return"Valid"===this.captchaStatus?Promise.resolve():new Promise((function(i,o){window.grecaptcha.execute(t).then((function(){e.timer&&clearTimeout(e.timer),e.timer=window.setTimeout(i,1e3)}))}))}return Promise.resolve()},t.prototype.onResetField=function(e){if(e.field===t.CAPTCHA_FIELD&&this.widgetIds.has(this.opts.element)){var i=this.widgetIds.get(this.opts.element);window.grecaptcha.reset(i)}},t.prototype.onIconPlaced=function(e){if(e.field===t.CAPTCHA_FIELD)if("invisible"===this.opts.size)e.iconElement.style.display="none";else{var i=document.getElementById(this.opts.element);i&&i.parentNode.insertBefore(e.iconElement,i.nextSibling)}},t.CAPTCHA_FIELD="g-recaptcha-response",t.DEFAULT_OPTIONS={backendVerificationUrl:"",badge:"bottomright",size:"normal",theme:"light"},t.LOADED_CALLBACK="___reCaptchaLoaded___",t}(t.Plugin);e.Recaptcha=r}));
