/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-sequence
 * @version 2.4.0
 */

define(["exports","@form-validation/core"],(function(e,t){"use strict";var i=function(e,t){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},i(e,t)};var l=t.utils.removeUndefined,n=function(e){function t(t){var i=e.call(this,t)||this;return i.invalidFields=new Map,i.opts=Object.assign({},{enabled:!0},l(t)),i.validatorHandler=i.onValidatorValidated.bind(i),i.shouldValidateFilter=i.shouldValidate.bind(i),i.fieldAddedHandler=i.onFieldAdded.bind(i),i.elementNotValidatedHandler=i.onElementNotValidated.bind(i),i.elementValidatingHandler=i.onElementValidating.bind(i),i}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function l(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(l.prototype=t.prototype,new l)}(t,e),t.prototype.install=function(){this.core.on("core.validator.validated",this.validatorHandler).on("core.field.added",this.fieldAddedHandler).on("core.element.notvalidated",this.elementNotValidatedHandler).on("core.element.validating",this.elementValidatingHandler).registerFilter("field-should-validate",this.shouldValidateFilter)},t.prototype.uninstall=function(){this.invalidFields.clear(),this.core.off("core.validator.validated",this.validatorHandler).off("core.field.added",this.fieldAddedHandler).off("core.element.notvalidated",this.elementNotValidatedHandler).off("core.element.validating",this.elementValidatingHandler).deregisterFilter("field-should-validate",this.shouldValidateFilter)},t.prototype.shouldValidate=function(e,t,i,l){return!this.isEnabled||!((!0===this.opts.enabled||!0===this.opts.enabled[e])&&this.invalidFields.has(t)&&!!this.invalidFields.get(t).length&&-1===this.invalidFields.get(t).indexOf(l))},t.prototype.onValidatorValidated=function(e){var t=this.invalidFields.has(e.element)?this.invalidFields.get(e.element):[],i=t.indexOf(e.validator);e.result.valid&&i>=0?t.splice(i,1):e.result.valid||-1!==i||t.push(e.validator),this.invalidFields.set(e.element,t)},t.prototype.onFieldAdded=function(e){e.elements&&this.clearInvalidFields(e.elements)},t.prototype.onElementNotValidated=function(e){this.clearInvalidFields(e.elements)},t.prototype.onElementValidating=function(e){this.clearInvalidFields(e.elements)},t.prototype.clearInvalidFields=function(e){var t=this;e.forEach((function(e){return t.invalidFields.delete(e)}))},t}(t.Plugin);e.Sequence=n}));
