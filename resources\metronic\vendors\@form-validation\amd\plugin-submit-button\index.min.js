/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-submit-button
 * @version 2.4.0
 */

define(["exports","@form-validation/core"],(function(t,e){"use strict";var i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},i(t,e)};var n=function(t){function e(e){var i=t.call(this,e)||this;return i.isFormValid=!1,i.isButtonClicked=!1,i.opts=Object.assign({},{aspNetButton:!1,buttons:function(t){return[].slice.call(t.querySelectorAll('[type="submit"]:not([formnovalidate])'))},liveMode:!0},e),i.submitHandler=i.handleSubmitEvent.bind(i),i.buttonClickHandler=i.handleClickEvent.bind(i),i.ignoreValidationFilter=i.ignoreValidation.bind(i),i}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}(e,t),e.prototype.install=function(){var t=this;if(this.core.getFormElement()instanceof HTMLFormElement){var e=this.core.getFormElement();this.submitButtons=this.opts.buttons(e),e.setAttribute("novalidate","novalidate"),e.addEventListener("submit",this.submitHandler),this.hiddenClickedEle=document.createElement("input"),this.hiddenClickedEle.setAttribute("type","hidden"),e.appendChild(this.hiddenClickedEle),this.submitButtons.forEach((function(e){e.addEventListener("click",t.buttonClickHandler)})),this.core.registerFilter("element-ignored",this.ignoreValidationFilter)}},e.prototype.uninstall=function(){var t=this,e=this.core.getFormElement();e instanceof HTMLFormElement&&e.removeEventListener("submit",this.submitHandler),this.submitButtons.forEach((function(e){e.removeEventListener("click",t.buttonClickHandler)})),this.hiddenClickedEle.parentElement.removeChild(this.hiddenClickedEle),this.core.deregisterFilter("element-ignored",this.ignoreValidationFilter)},e.prototype.handleSubmitEvent=function(t){this.validateForm(t)},e.prototype.handleClickEvent=function(t){var e=t.currentTarget;if(this.isButtonClicked=!0,e instanceof HTMLElement)if(this.opts.aspNetButton&&!0===this.isFormValid);else{this.core.getFormElement().removeEventListener("submit",this.submitHandler),this.clickedButton=t.target;var i=this.clickedButton.getAttribute("name"),n=this.clickedButton.getAttribute("value");i&&n&&(this.hiddenClickedEle.setAttribute("name",i),this.hiddenClickedEle.setAttribute("value",n)),this.validateForm(t)}},e.prototype.validateForm=function(t){var e=this;this.isEnabled&&(t.preventDefault(),this.core.validate().then((function(t){"Valid"===t&&e.opts.aspNetButton&&!e.isFormValid&&e.clickedButton&&(e.isFormValid=!0,e.clickedButton.removeEventListener("click",e.buttonClickHandler),e.clickedButton.click())})))},e.prototype.ignoreValidation=function(t,e,i){return!!this.isEnabled&&(!this.opts.liveMode&&!this.isButtonClicked)},e}(e.Plugin);t.SubmitButton=n}));
