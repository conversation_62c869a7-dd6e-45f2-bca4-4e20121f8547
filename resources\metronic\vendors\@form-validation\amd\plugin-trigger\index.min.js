/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-trigger
 * @version 2.4.0
 */

define(["exports","@form-validation/core"],(function(e,t){"use strict";var n=function(e,t){return n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},n(e,t)};var r=function(e){function t(t){var n=e.call(this,t)||this;n.handlers=[],n.timers=new Map;var r=document.createElement("div");return n.defaultEvent="oninput"in r?"input":"keyup",n.opts=Object.assign({},{delay:0,event:n.defaultEvent,threshold:0},t),n.fieldAddedHandler=n.onFieldAdded.bind(n),n.fieldRemovedHandler=n.onFieldRemoved.bind(n),n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}(t,e),t.prototype.install=function(){this.core.on("core.field.added",this.fieldAddedHandler).on("core.field.removed",this.fieldRemovedHandler)},t.prototype.uninstall=function(){this.handlers.forEach((function(e){return e.element.removeEventListener(e.event,e.handler)})),this.handlers=[],this.timers.forEach((function(e){return window.clearTimeout(e)})),this.timers.clear(),this.core.off("core.field.added",this.fieldAddedHandler).off("core.field.removed",this.fieldRemovedHandler)},t.prototype.prepareHandler=function(e,t){var n=this;t.forEach((function(t){var r=[];if(n.opts.event&&!1===n.opts.event[e])r=[];else if(n.opts.event&&n.opts.event[e]&&"function"!=typeof n.opts.event[e])r=n.opts.event[e].split(" ");else if("string"==typeof n.opts.event&&n.opts.event!==n.defaultEvent)r=n.opts.event.split(" ");else{var o=t.getAttribute("type"),i=t.tagName.toLowerCase();r=["radio"===o||"checkbox"===o||"file"===o||"select"===i?"change":n.ieVersion>=10&&t.getAttribute("placeholder")?"keyup":n.defaultEvent]}r.forEach((function(r){var o=function(r){return n.handleEvent(r,e,t)};n.handlers.push({element:t,event:r,field:e,handler:o}),t.addEventListener(r,o)}))}))},t.prototype.handleEvent=function(e,t,n){var r=this;if(this.isEnabled&&this.exceedThreshold(t,n)&&this.core.executeFilter("plugins-trigger-should-validate",!0,[t,n])){var o=function(){return r.core.validateElement(t,n).then((function(o){r.core.emit("plugins.trigger.executed",{element:n,event:e,field:t})}))},i=this.opts.delay[t]||this.opts.delay;if(0===i)o();else{var d=this.timers.get(n);d&&window.clearTimeout(d),this.timers.set(n,window.setTimeout(o,1e3*i))}}},t.prototype.onFieldAdded=function(e){this.handlers.filter((function(t){return t.field===e.field})).forEach((function(e){return e.element.removeEventListener(e.event,e.handler)})),this.prepareHandler(e.field,e.elements)},t.prototype.onFieldRemoved=function(e){this.handlers.filter((function(t){return t.field===e.field&&e.elements.indexOf(t.element)>=0})).forEach((function(e){return e.element.removeEventListener(e.event,e.handler)}))},t.prototype.exceedThreshold=function(e,t){var n=0!==this.opts.threshold[e]&&0!==this.opts.threshold&&(this.opts.threshold[e]||this.opts.threshold);if(!n)return!0;var r=t.getAttribute("type");return-1!==["button","checkbox","file","hidden","image","radio","reset","submit"].indexOf(r)||this.core.getElementValue(e,t).length>=n},t}(t.Plugin);e.Trigger=r}));
