/** 
 * FormValidation (https://formvalidation.io)
 * The best validation library for JavaScript
 * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * @license https://formvalidation.io/license
 * @package @form-validation/plugin-turnstile
 * @version 2.4.0
 */

define(["exports","@form-validation/core"],(function(t,e){"use strict";var i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},i(t,e)};var a=e.utils.fetch,n=e.utils.removeUndefined,r=function(t){function e(i){var a=t.call(this,i)||this;return a.widgetIds=new Map,a.captchaStatus="NotValidated",a.captchaContainer="",a.opts=Object.assign({},e.DEFAULT_OPTIONS,n(i)),a.fieldResetHandler=a.onResetField.bind(a),a.preValidateFilter=a.preValidate.bind(a),a.iconPlacedHandler=a.onIconPlaced.bind(a),a.captchaContainer=a.opts.element.startsWith("#")?a.opts.element:"#".concat(a.opts.element),a}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function a(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(a.prototype=e.prototype,new a)}(e,t),e.prototype.install=function(){var t=this;this.core.on("core.field.reset",this.fieldResetHandler).on("plugins.icon.placed",this.iconPlacedHandler).registerFilter("validate-pre",this.preValidateFilter);var i=void 0===window[e.LOADED_CALLBACK]?function(){}:window[e.LOADED_CALLBACK];window[e.LOADED_CALLBACK]=function(){i();var n=t.getTurnstileInstance().render(t.captchaContainer,t.buildTurnstileRenderOptions());t.widgetIds.set(t.captchaContainer,n),t.core.addField(e.CAPTCHA_FIELD,{validators:{promise:{message:t.opts.message,promise:function(i){var n,r=t.widgetIds.has(t.captchaContainer)?t.getTurnstileInstance().getResponse(t.widgetIds.get(t.captchaContainer)):i.value;return""===r?(t.captchaStatus="Invalid",Promise.resolve({valid:!1})):""===t.opts.backendVerificationUrl?(t.captchaStatus="Valid",Promise.resolve({valid:!0})):"Valid"===t.captchaStatus?Promise.resolve({valid:!0}):a(t.opts.backendVerificationUrl,{method:"POST",params:(n={},n[e.CAPTCHA_FIELD]=r,n)}).then((function(e){var i="true"==="".concat(e.success);return t.captchaStatus=i?"Valid":"Invalid",Promise.resolve({meta:e,valid:i})})).catch((function(e){return t.captchaStatus="NotValidated",Promise.reject({valid:!1})}))}}}})};var n=this.getScript();if(!document.body.querySelector('script[src="'.concat(n,'"]'))){var r=document.createElement("script");r.type="text/javascript",r.async=!0,r.defer=!0,r.src=n,document.body.appendChild(r)}},e.prototype.uninstall=function(){var t=this;delete window[e.LOADED_CALLBACK],this.core.off("core.field.reset",this.fieldResetHandler).off("plugins.icon.placed",this.iconPlacedHandler).deregisterFilter("validate-pre",this.preValidateFilter),this.widgetIds.forEach((function(e,i,a){t.getTurnstileInstance().remove(i)})),this.widgetIds.clear();var i=this.getScript();[].slice.call(document.body.querySelectorAll('script[src="'.concat(i,'"]'))).forEach((function(t){return t.parentNode.removeChild(t)})),this.core.removeField(e.CAPTCHA_FIELD)},e.prototype.onEnabled=function(){this.core.enableValidator(e.CAPTCHA_FIELD,"promise")},e.prototype.onDisabled=function(){this.core.disableValidator(e.CAPTCHA_FIELD,"promise")},e.prototype.buildTurnstileRenderOptions=function(){var t=this;return{callback:function(){""===t.opts.backendVerificationUrl&&(t.captchaStatus="Valid",t.core.updateFieldStatus(e.CAPTCHA_FIELD,"Valid"))},"error-callback":function(){t.captchaStatus="Invalid",t.core.updateFieldStatus(e.CAPTCHA_FIELD,"Invalid")},"expired-callback":function(){t.captchaStatus="NotValidated",t.core.updateFieldStatus(e.CAPTCHA_FIELD,"NotValidated")},sitekey:this.opts.siteKey,action:this.opts.action,appearance:this.opts.appearance,cData:this.opts.cData,language:this.opts.language,size:this.opts.size,"refresh-expired":this.opts.refreshExpired,retry:this.opts.retry,"retry-interval":this.opts.retryInterval,tabindex:this.opts.tabIndex,theme:this.opts.theme}},e.prototype.getTurnstileInstance=function(){return window.turnstile},e.prototype.getScript=function(){return"https://challenges.cloudflare.com/turnstile/v0/api.js?onload=".concat(e.LOADED_CALLBACK,"&render=explicit")},e.prototype.preValidate=function(){return this.isEnabled&&"execute"===this.opts.appearance&&this.widgetIds.has(this.captchaContainer)&&"Valid"!==this.captchaStatus&&this.getTurnstileInstance().execute(this.captchaContainer,this.buildTurnstileRenderOptions()),Promise.resolve()},e.prototype.onResetField=function(t){if(t.field===e.CAPTCHA_FIELD&&this.widgetIds.has(this.captchaContainer)){var i=this.widgetIds.get(this.captchaContainer);this.getTurnstileInstance().reset(i)}},e.prototype.onIconPlaced=function(t){if(t.field===e.CAPTCHA_FIELD)if("execute"===this.opts.appearance)t.iconElement.style.display="none";else{var i=document.getElementById(this.captchaContainer);i&&i.parentNode.insertBefore(t.iconElement,i.nextSibling)}},e.CAPTCHA_FIELD="cf-turnstile-response",e.DEFAULT_OPTIONS={backendVerificationUrl:"",appearance:"always",language:"auto",refreshExpired:"auto",retry:"auto",size:"normal",tabIndex:0,theme:"auto"},e.LOADED_CALLBACK="___turnstileLoaded___",e}(e.Plugin);t.Turnstile=r}));
