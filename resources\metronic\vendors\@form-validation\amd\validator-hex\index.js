define(['exports'], (function (exports) { 'use strict';

    /**
     * FormValidation (https://formvalidation.io)
     * The best validation library for JavaScript
     * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
     */
    function hex() {
        return {
            /**
             * Return true if and only if the input value is a valid hexadecimal number
             */
            validate: function (input) {
                return {
                    valid: input.value === '' || /^[0-9a-fA-F]+$/.test(input.value),
                };
            },
        };
    }

    exports.hex = hex;

}));
