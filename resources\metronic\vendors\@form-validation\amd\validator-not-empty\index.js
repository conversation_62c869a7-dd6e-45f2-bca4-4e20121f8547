define(['exports'], (function (exports) { 'use strict';

    /**
     * FormValidation (https://formvalidation.io)
     * The best validation library for JavaScript
     * (c) 2013 - 2023 <PERSON><PERSON><PERSON> <<EMAIL>>
     */
    function notEmpty() {
        return {
            validate: function (input) {
                var trim = !!input.options && !!input.options.trim;
                var value = input.value;
                return {
                    valid: (!trim && value !== '') || (trim && value !== '' && value.trim() !== ''),
                };
            },
        };
    }

    exports.notEmpty = notEmpty;

}));
