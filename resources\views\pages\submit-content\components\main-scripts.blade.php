<script>
  // Content Management Application
  const ContentManager = {
    // Configuration constants
    CONFIG: {
      STATUS_URL_MAP: {
        'Draft': 'not_submitted',
        'Pending': 'pending',
        'Rejected': 'rejected',
        'Approved': 'approved',
        'Published': 'published'
      },
      STATUS_TITLE_MAP: {
        'Draft': 'Not Submitted',
        'Pending': 'Pending',
        'Rejected': 'Rejected',
        'Approved': 'Approved',
        'Published': 'Published'
      },
      BADGE_CLASS_MAP: {
        'Draft': 'badge-dark',
        'Pending': 'badge-warning',
        'Rejected': 'badge-danger',
        'Approved': 'badge-primary',
        'Published': 'badge-success'
      },
      FILTER_MAP: {
        index: { name: "Semu<PERSON>", icon: "fa-list" },
        image: { name: "Image", icon: "fa-image" },
        video: { name: "Video", icon: "fa-video" },
        audio: { name: "Audio", icon: "fa-music" },
        document: { name: "Document", icon: "fa-file" }
      },
      PAGE_NOTES: {
        draft: { note: "Need To Be Submitted"},
        pending: { note: "Waiting To Be Reviewed"},
        reviewed: { note: "Have Been Reviewed"},
        rejected: { note: "Have Been Rejected"},
        approved: { note: "Have Been Approved"},
        published: { note: "Are Published"}
      },
      FLAG_MAP: {
        reviewed: { name: "Semua" },
        approved: { name: "Approved" },
        rejected: { name: "Rejected" }
      }
    },

    // State variables
    state: {
      tabsEl: null,
      tabs: null,
      authorId: "{{ auth()->user()->id }}",
      keys: ["draft", "pending", "reviewed", "published", "approved", "rejected"],
      status: "Draft",
      filter: "index",
      flag: "Reviewed",
      selectedIds: [],
      isMultipleSelect: false,
      contentCounts: {},
      smScreen: false
    },

    // Cached DOM elements
    elements: {
      pageTitle: null,
      selectionBanner: null,
      btnUpdate: null,
      btnLoading: null,
      detailContent: null,
      detailContentForm: null
    },

    // Template content
    templates: {
      formContent: `@include('pages.submit-content.form')`,
      showContent: `@include('pages.submit-content.show')`,
      emptyContent: `@include('pages.submit-content.empty')`
    }

    // Initialize application
    init() {
      this.cacheElements();
      this.initializeTabs();
      this.parseUrlParameters();
      this.initializeContentCounts();
      this.setupEventHandlers();
      this.initializeUI();
      this.loadInitialContent();
    },

    // Cache DOM elements for better performance
    cacheElements() {
      this.elements.pageTitle = $("#page-title");
      this.elements.selectionBanner = $("#banner-container");
      this.elements.btnUpdate = $("#group-btn-update");
      this.elements.btnLoading = $(".btn-loading");
      this.state.smScreen = window.matchMedia("(max-width: 991px)").matches;
    },

    // Initialize tabs
    initializeTabs() {
      this.state.tabsEl = document.querySelector('#my_tabs');
      const options = { hiddenClass: 'hidden' };
      this.state.tabs = new KTTabs(this.state.tabsEl, options);
    },

    // Parse URL parameters
    parseUrlParameters() {
      const urlParams = new URLSearchParams(window.location.search);
      const tabParam = urlParams.get('tab');
      const filterParam = urlParams.get('filter');

      // Map URL parameter to status
      const tabStatusMap = {
        'not_submitted': 'Draft',
        'pending': 'Pending',
        'rejected': 'Rejected',
        'approved': 'Approved',
        'published': 'Published'
      };

      this.state.status = (tabParam && tabStatusMap[tabParam]) ? tabStatusMap[tabParam] : "Draft";
      this.state.filter = filterParam || "index";
    },

    // Initialize content counts structure
    initializeContentCounts() {
      this.state.contentCounts = {};
      this.state.keys.forEach(key => {
        this.state.contentCounts[key] = {
          index: 0, image: 0, video: 0, audio: 0, document: 0
        };
      });
    },

    // Adjust banner position based on sidebar width
    adjustBannerPosition() {
      const windowWidth = $(window).width();
      const banner = this.elements.selectionBanner;

      if (windowWidth >= 992) { // Desktop view
        const sidebarWidth = $(".app-sidebar").width() || 280;
        banner.css({
          "left": sidebarWidth + "px",
          "width": (windowWidth - sidebarWidth) + "px",
          "z-index": "100"
        });
      } else { // Mobile view
        banner.css({
          "left": "0",
          "width": "100%",
          "z-index": "100"
        });
      }
    },

    // Initialize UI components
    initializeUI() {
      this.elements.detailContent = $("#detail-" + this.state.status.toLowerCase());

      // Find and activate the correct tab based on status
      if (this.state.status !== "Draft") {
        const tabToActivate = document.querySelector(`[data-status="${this.state.status}"]`);
        if (tabToActivate) {
          this.state.tabs.show(tabToActivate);
        }
      }

      // Activate the correct filter
      $(".filter-media").removeClass("active");
      $(`.filter-media[data-filter="${this.state.filter}"]`).addClass("active");

      // Initialize badge and upload button
      $("#status-badge").text("0").removeClass('hidden');

      if (this.state.status !== "Draft") {
        $("#upload-now-button").hide();
      }

      // Initialize banner position and sidebar
      this.adjustBannerPosition();
      this.updateSidebarActiveState();
      this.injectCustomCSS();
    },

    // Update sidebar menu active state
    updateSidebarActiveState() {
      const currentTabParam = this.CONFIG.STATUS_URL_MAP[this.state.status];

      $("#submit-content-submenu .menu-link").removeClass("bg-gray-50 dark:bg-coal-300 bg-gray-800");
      $(`#submit-content-submenu a[href*="tab=${currentTabParam}"]`).addClass("bg-gray-50 dark:bg-coal-300");
    },

    // Inject custom CSS (only once)
    injectCustomCSS() {
      if (!$("#sidebar-bullet-fix").length) {
        $("head").append(`
          <style id="sidebar-bullet-fix">
            #submit-content-submenu .menu-link::before {
              opacity: 0 !important;
            }
            #submit-content-submenu .menu-link.bg-gray-50::before {
              opacity: 1 !important;
            }
          </style>
        `);
      }
    },

    // Load initial content
    loadInitialContent() {
      this.updatePageTitle(this.state.status);
      this.getContent(this.state.status, this.state.filter, this.state.authorId);
    },

    // Get content from server
    getContent(status, mediaType, author, url) {
      this.updatePageTitle(status);

      $.ajax({
        url: url || "{{ route('get-content-author') }}",
        type: "GET",
        data: { status, media_type: mediaType, author },
        beforeSend: () => {
          $(".loading-content, .toolbar-skeleton").show();
          $("#filter-menu, #tab-note, #flag-menu").hide();
        },
        success: (response) => {
          this.processContentResponse(response, status);
        },
        error: (xhr, status, error) => {
          console.error("Error loading content:", status, error);
        }
      });
    },

    // Process content response from server
    processContentResponse(response, status) {
      const count = response.data.count;
      console.log("Count data:", count);

      // Update counts for all statuses
      this.state.keys.forEach(key => {
        const statusKey = key.toLowerCase();
        const counts = this.state.contentCounts[statusKey];

        counts.index = parseInt(count[statusKey] || 0);
        counts.image = parseInt(count[`${statusKey}_image`] || 0);
        counts.video = parseInt(count[`${statusKey}_video`] || 0);
        counts.audio = parseInt(count[`${statusKey}_audio`] || 0);
        counts.document = parseInt(count[`${statusKey}_document`] || 0);
      });

      // Special case: For Approved tab, include Published content count
      if (status === "Approved") {
        const approved = this.state.contentCounts.approved;
        approved.index = parseInt(count.approved || 0) + parseInt(count.published || 0);
        approved.image = parseInt(count.approved_image || 0) + parseInt(count.published_image || 0);
        approved.video = parseInt(count.approved_video || 0) + parseInt(count.published_video || 0);
        approved.audio = parseInt(count.approved_audio || 0) + parseInt(count.published_audio || 0);
        approved.document = parseInt(count.approved_document || 0) + parseInt(count.published_document || 0);
      }

      // Update UI
      this.updatePageTitle(status);
      $("#tab-note, #filter-menu").show();
      $(".toolbar-skeleton, .loading-content").hide();

      contentData(response.data.content);
      this.updateFilter(this.state.filter, status);

      if (["Reviewed"].includes(status)) {
        $("#flag-menu").show();
        this.updateFlag(status, this.state.filter);
      }
    },

    // Update filter UI and counts
    updateFilter(filter, status) {
      const statusKey = status.toLowerCase();
      const filterKey = filter.toLowerCase();

      // Ensure contentCounts exists for this status
      if (!this.state.contentCounts[statusKey]) {
        this.state.contentCounts[statusKey] = { index: 0, image: 0, video: 0, audio: 0, document: 0 };
      }

      const count = this.state.contentCounts[statusKey][filterKey] ?? 0;
      const filterInfo = this.CONFIG.FILTER_MAP[filterKey];

      // Update filter button
      $("#btn-filter").html(`
        <span class="flex items-center me-1">
         <i class="fa-solid ${filterInfo.icon}"></i>
        </span>
        <span class="hidden md:inline text-nowrap">
         ${filterInfo.name} (${count})
        </span>
        <span class="inline md:hidden text-nowrap">
         ${filterInfo.name} (${count})
        </span>
        <span class="flex items-center lg:ms-4">
         <i class="ki-filled ki-down !text-xs"></i>
        </span>
      `);

      // Update filter menu items
      Object.keys(this.CONFIG.FILTER_MAP).forEach(media => {
        const mediaCount = this.state.contentCounts[statusKey][media] ?? 0;
        const mediaInfo = this.CONFIG.FILTER_MAP[media];
        $(`#filter-item-${media}`).text(`${mediaInfo.name} (${mediaCount})`);
      });

      // Update tab counts
      this.state.keys.forEach(key => {
        const tabCount = parseInt(this.state.contentCounts[key].index) || 0;
        $(`#tab-item-${key}`).text(tabCount > 0 ? tabCount : "");
      });

      // Update tab note
      const statusCount = parseInt(this.state.contentCounts[statusKey].index) || 0;
      const noteText = this.CONFIG.PAGE_NOTES[statusKey]?.note || "Unknown Status";
      $('#tab-note').text(`${statusCount} File(s) ${noteText}`);

      this.updatePageTitle(status);
    },

    // Update flag UI and counts
    updateFlag(flag, filter) {
      const flagKey = flag.toLowerCase();
      const filterKey = filter.toLowerCase();

      // Ensure contentCounts exists for this flag
      if (!this.state.contentCounts[flagKey]) {
        this.state.contentCounts[flagKey] = { index: 0, image: 0, video: 0, audio: 0, document: 0 };
      }

      const count = this.state.contentCounts[flagKey][filterKey] ?? 0;
      const flagInfo = this.CONFIG.FLAG_MAP[flagKey];

      $("#btn-flag").html(`
        <span class="hidden md:inline text-nowrap">
          ${flagInfo.name} (${count})
        </span>
        <span class="inline md:hidden text-nowrap">
          ${flagInfo.name} (${count})
        </span>
        <span class="flex items-center lg:ms-4">
          <i class="ki-filled ki-down !text-xs"></i>
        </span>
      `);

      ["reviewed", "approved", "rejected"].forEach(media => {
        const mediaCount = this.state.contentCounts[media][filterKey] ?? 0;
        const mediaInfo = this.CONFIG.FLAG_MAP[media];
        $(`#flag-item-${media}`).text(`${mediaInfo.name} (${mediaCount})`);
      });
    },

    // Update page title and badge
    updatePageTitle(currentStatus) {
      const statusKey = currentStatus.toLowerCase();
      const title = this.CONFIG.STATUS_TITLE_MAP[currentStatus];
      const badgeClass = this.CONFIG.BADGE_CLASS_MAP[currentStatus];

      // Ensure contentCounts exists for this status
      if (!this.state.contentCounts[statusKey]) {
        this.state.contentCounts[statusKey] = { index: 0, image: 0, video: 0, audio: 0, document: 0 };
      }

      const count = parseInt(this.state.contentCounts[statusKey].index) || 0;

      // Update title and badge
      $("#status-title").text(`Submit Content - ${title}`);
      $("#status-badge").text(count).removeClass('hidden badge-dark badge-warning badge-danger badge-primary badge-success').addClass(badgeClass);

      // Show/hide upload button
      $("#upload-now-button").toggle(currentStatus === 'Draft');

      // Update sidebar
      this.updateSidebarActiveState();
    },

    // Switch to a different tab
    switchTab(tabElement) {
      if (!tabElement) return;

      const newStatus = $(tabElement).data('status');
      console.log('Switching to tab:', newStatus);

      // Update state
      this.state.status = newStatus;
      this.state.selectedIds = [];

      // Update tab active state
      $(".tab").removeClass("active");
      $(tabElement).addClass("active");

      // Update detail content
      const newDetailSelector = "#detail-" + newStatus.toLowerCase();
      const newDetailContent = $(newDetailSelector);

      if (newDetailContent.length) {
        if (this.elements.detailContent) {
          this.elements.detailContent.html(this.templates.emptyContent);
        }
        this.elements.detailContent = newDetailContent;
      } else {
        console.error('Detail content not found:', newDetailSelector);
      }

      // Update UI state
      this.elements.btnUpdate.addClass("hidden").removeClass("flex");

      // Update filter and page title
      this.updateFilter(this.state.filter, this.state.status);
      this.updatePageTitle(this.state.status);
    },
    // Setup all event handlers
    setupEventHandlers() {
      // Tab click handler
      $(document).on("click", ".tab", (e) => {
        const tabElement = e.currentTarget;
        console.log('Tab clicked:', $(tabElement).data('status'));

        this.switchTab(tabElement);
        this.getContent(this.state.status, this.state.filter, this.state.authorId);
        this.updateURL();
        this.updateSidebarActiveState();
      });

      // Filter click handler
      $(document).on("click", ".filter-media", (e) => {
        this.state.filter = $(e.currentTarget).data("filter");
        this.state.selectedIds = [];

        this.elements.detailContent.html(this.templates.emptyContent);
        this.elements.btnUpdate.addClass("hidden").removeClass("flex");

        $(".filter-media").removeClass("active");
        $(e.currentTarget).addClass("active");

        this.updateFilter(this.state.filter, this.state.status);
        this.getContent(this.state.status, this.state.filter, this.state.authorId);
        this.updateURL();
      });

      // Flag click handler
      $(document).on("click", ".flag", (e) => {
        this.state.flag = $(e.currentTarget).data("flag");
        this.state.selectedIds = [];

        this.elements.detailContent.html(this.templates.emptyContent);
        this.elements.btnUpdate.addClass("hidden").removeClass("flex");

        $(".flag").removeClass("active");
        $(e.currentTarget).addClass("active");

        this.updateFlag(this.state.flag, this.state.filter);
        this.getContent(this.state.flag, this.state.filter, this.state.authorId);
      });

      // Window resize handler
      $(window).on('resize', () => {
        if (this.state.isMultipleSelect) {
          this.adjustBannerPosition();
        }
        this.state.smScreen = window.matchMedia("(max-width: 991px)").matches;
      });

      this.setupCardHandlers();
      this.setupButtonHandlers();
      this.setupSidebarHandlers();
    },

    // Update URL parameters
    updateURL() {
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.set('tab', this.CONFIG.STATUS_URL_MAP[this.state.status]);
      newUrl.searchParams.set('filter', this.state.filter);
      window.history.pushState({}, '', newUrl);
    },

    // Setup card-related event handlers
    setupCardHandlers() {
      // Card click handler
      $(document).on("click", ".select-card", (e) => {
        const card = $(e.currentTarget);
        const cardId = card.data("id");

        // Skip if clicking on checkbox area
        if ($(e.target).closest('.multiple-select').length > 0 ||
            $(e.target).hasClass('unchecked') ||
            $(e.target).hasClass('checked')) {
          return;
        }

        // In multiple select mode, just clear details
        if (this.state.isMultipleSelect) {
          this.elements.detailContent.html(this.templates.emptyContent);
          return;
        }

        // Show details for single selection
        this.handleShowDetails(card, cardId);
      });

      // Checkbox click handler
      $(document).on("click", ".multiple-select, .unchecked, .checked", (e) => {
        e.stopPropagation();
        e.preventDefault();

        const card = $(e.target).closest(".select-card");
        const cardId = card.data("id");
        const isSelected = this.state.selectedIds.includes(cardId);

        // Always enter multiple selection mode when clicking checkbox
        this.state.isMultipleSelect = true;

        // Toggle selection
        if (isSelected) {
          this.deselectCard(card, cardId);
        } else {
          this.selectCard(card, cardId);
        }

        this.updateSelectionBanner();

        // Exit multiple selection if no items selected
        if (this.state.selectedIds.length === 0) {
          this.exitMultipleSelection();
        }

        // Hide details in multiple selection mode
        if (this.state.isMultipleSelect) {
          this.elements.detailContent.html(this.templates.emptyContent);
        }
      });
    },

    // Handle showing card details
    handleShowDetails(card, cardId) {
      // Highlight the card visually
      $(".select-card").removeClass("border-blue-500").addClass("border-gray-300");
      card.addClass("border-blue-500").removeClass("border-gray-300");

      // Update selectedIds for tracking
      this.state.selectedIds = [cardId];

      // Show detail content
      this.state.status === "Draft" ? updateForm() : updateShow();
    },

    // Select a card
    selectCard(card, cardId) {
      if (!this.state.selectedIds.includes(cardId)) {
        this.state.selectedIds.push(cardId);
      }

      card.addClass("border-blue-500").removeClass("border-gray-300");

      const checkbox = card.find(".multiple-select");
      const uncheckedIcon = card.find(".unchecked");
      const checkedIcon = card.find(".checked");

      checkbox.removeClass("bg-neutral-600 hover:bg-neutral-800 opacity-0 group-hover:opacity-100")
              .addClass("bg-neutral-900 opacity-100");
      uncheckedIcon.hide();
      checkedIcon.show();
    },

    // Deselect a card
    deselectCard(card, cardId) {
      this.state.selectedIds = this.state.selectedIds.filter(id => id !== cardId);
      card.removeClass("border-blue-500").addClass("border-gray-300");

      const checkbox = card.find(".multiple-select");
      const uncheckedIcon = card.find(".unchecked");
      const checkedIcon = card.find(".checked");

      checkbox.removeClass("bg-neutral-900 opacity-100")
              .addClass("bg-neutral-600 hover:bg-neutral-800 opacity-0 group-hover:opacity-100");
      uncheckedIcon.show();
      checkedIcon.hide();
    },

    // Update selection banner
    updateSelectionBanner() {
      this.adjustBannerPosition();
      this.elements.selectionBanner.show();

      if (this.state.smScreen) {
        this.elements.pageTitle.hide();
      }

      $('#banner-title').text(`${this.state.selectedIds.length} item dipilih`);
    },

    // Exit multiple selection mode
    exitMultipleSelection() {
      this.state.isMultipleSelect = false;
      this.elements.selectionBanner.hide();

      if (this.state.smScreen) {
        this.elements.pageTitle.show();
      }
    },

    // Setup button event handlers
    setupButtonHandlers() {
      // Delete button handler
      $(document).on("click", ".btn-delete", (e) => {
        e.preventDefault();
        const contentId = $(e.currentTarget).data("id");
        deleteContent(contentId);
      });

      // Bulk delete handler
      $('#btn-bulk-delete').on('click', (e) => {
        e.preventDefault();
        bulkDelete(this.state.selectedIds);
      });

      // Close banner handler
      $('#btn-close-banner').on('click', () => {
        $(".select-card").removeClass("border-blue-500").addClass("border-gray-300");
        $(".multiple-select").removeClass("bg-neutral-900 opacity-100")
                            .addClass("bg-neutral-600 hover:bg-neutral-800 opacity-0 group-hover:opacity-100");
        $(".unchecked").show();
        $(".checked").hide();

        this.state.selectedIds = [];
        this.exitMultipleSelection();
        this.elements.detailContent.html(this.templates.emptyContent);
      });

      // Save button handler
      $('#btn-save').on('click', (e) => {
        Swal.fire({
          title: "Are you sure?",
          text: "Your data will saved as draft.",
          icon: "warning",
          showCancelButton: true,
          customClass: {
            confirmButton: 'btn btn-light',
            cancelButton: 'btn btn-danger',
          },
          confirmButtonText: "Yes, save it!"
        }).then((result) => {
          if (result.isConfirmed) {
            $('#update-type').val('update');
            this.elements.btnLoading.addClass("flex").removeClass("hidden");
            this.elements.btnUpdate.removeClass("flex").addClass("hidden");
            this.elements.detailContentForm.submit();
          }
        });
      });

      // Submit button handler
      $('#btn-submit').on('click', () => {
        Swal.fire({
          title: "Are you sure?",
          text: "You won't be able to revert this!",
          icon: "warning",
          showCancelButton: true,
          customClass: {
            confirmButton: 'btn btn-light',
            cancelButton: 'btn btn-danger',
          },
          confirmButtonText: "Yes, submit it!"
        }).then((result) => {
          if (result.isConfirmed) {
            $('#update-type').val('submit');
            this.elements.btnLoading.addClass("flex").removeClass("hidden");
            this.elements.btnUpdate.removeClass("flex").addClass("hidden");
            this.elements.detailContentForm.submit();
          }
        });
      });
    },

    // Setup sidebar navigation handlers
    setupSidebarHandlers() {
      $('#submit-content-submenu a').on('click', (e) => {
        console.log('--- Sidebar menu item clicked ---');

        // Only handle clicks if we're already on the submit-content page
        if (!window.location.pathname.includes('/submit-content')) {
          return; // Let the link work normally
        }

        e.preventDefault();

        // Get the tab parameter from the URL
        const href = $(e.currentTarget).attr('href');
        const url = new URL(href, window.location.origin);
        const tabParam = url.searchParams.get('tab');
        console.log('Tab parameter from URL:', tabParam);

        // Map URL parameter to status
        const tabStatusMap = {
          'not_submitted': 'Draft',
          'pending': 'Pending',
          'rejected': 'Rejected',
          'approved': 'Approved',
          'published': 'Published'
        };

        // Find and switch to the corresponding tab
        if (tabParam && tabStatusMap[tabParam]) {
          const newStatus = tabStatusMap[tabParam];
          console.log('Switching to status:', newStatus);

          const tabElement = document.querySelector(`[data-status="${newStatus}"]`);
          if (!tabElement) {
            console.error('Tab element not found for status:', newStatus);
            return;
          }

          // Update the tab and UI state
          this.switchTab(tabElement);

          // Update URL and get content
          this.updateURL();
          this.getContent(newStatus, this.state.filter, this.state.authorId);
          this.updateSidebarActiveState();
        }
      });
    }
  };

  // Initialize the application when document is ready
  $(document).ready(function() {
    ContentManager.init();
  });

    // Handle sidebar navigation clicks
    $('#submit-content-submenu a').on('click', function(e) {
      console.log('--- Sidebar menu item clicked ---');
      
      // Only handle clicks if we're already on the submit-content page
      if (!window.location.pathname.includes('/submit-content')) {
        return; // Let the link work normally if not on submit-content page
      }
      
      e.preventDefault();

      // Get the tab parameter from the URL
      const href = $(this).attr('href');
      const url = new URL(href, window.location.origin);
      const tabParam = url.searchParams.get('tab');
      console.log('Tab parameter from URL:', tabParam);

      // Map URL parameter to status
      const tabStatusMap = {
        'not_submitted': 'Draft',
        'pending': 'Pending',
        'rejected': 'Rejected',
        'approved': 'Approved',
        'published': 'Published'
      };

      // Find and click the corresponding tab
      if (tabParam && tabStatusMap[tabParam]) {
        const newStatus = tabStatusMap[tabParam];
        console.log('Switching to status:', newStatus);
        
        // Find the corresponding tab element
        const tabElement = document.querySelector(`[data-status="${newStatus}"]`);
        if (!tabElement) {
          console.error('Tab element not found for status:', newStatus);
          return;
        }
        
        // Update the tab and UI state
        switchTab(tabElement);
        
        // Update URL with new tab and current filter
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.set('tab', tabParam);
        newUrl.searchParams.set('filter', filter);
        window.history.pushState({}, '', newUrl);
        
        // Get the content for the new tab
        getContent(newStatus, filter, authorId);
        
        // Update sidebar menu active state
        $("#submit-content-submenu .menu-link").removeClass("bg-gray-50 dark:bg-coal-300");
        $(this).addClass("bg-gray-50 dark:bg-coal-300");
      }
    });
  });
</script>
